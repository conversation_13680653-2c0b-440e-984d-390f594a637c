@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@500&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  width: 100%;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
  padding: 20px;
}

.calculator {
  border: none;
  padding: 30px;
  border-radius: 24px;
  background: linear-gradient(145deg, #3a4a5c 0%, #2c3e50 100%);
  backdrop-filter: blur(15px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 15px 30px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  width: 100%;
  max-width: 400px;
}

.calculator::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(149, 165, 166, 0.4),
    rgba(255, 255, 255, 0.3),
    rgba(149, 165, 166, 0.4),
    transparent
  );
}

input {
  width: 100%;
  border: none;
  padding: 24px 20px;
  margin: 0 0 20px 0;
  background: linear-gradient(145deg, #4a5568, #2d3748);
  border-radius: 16px;
  box-shadow: inset 0 3px 10px rgba(0, 0, 0, 0.3),
    inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: clamp(28px, 8vw, 40px);
  text-align: right;
  cursor: pointer;
  color: #e2e8f0;
  font-weight: 500;
  transition: all 0.2s ease;
}

input:focus {
  outline: none;
  box-shadow: inset 0 3px 10px rgba(0, 0, 0, 0.4),
    inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 0 3px rgba(149, 165, 166, 0.3),
    0 1px 0 rgba(255, 255, 255, 0.1);
}

input::placeholder {
  color: #a0aec0;
}

.calculator > div {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-bottom: 8px;
}

button {
  border: none;
  width: 100%;
  height: 65px;
  margin: 4px;
  border-radius: 18px;
  background: linear-gradient(145deg, #4a5568, #2d3748);
  color: #e2e8f0;
  font-size: clamp(16px, 4vw, 20px);
  font-weight: 600;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  min-height: 50px;
}

button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4), 0 6px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

button:active {
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.equalBtn {
  background: linear-gradient(145deg, #4a5568, #1a202c);
  color: #81c784;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(129, 199, 132, 0.2);
  border: 1px solid rgba(129, 199, 132, 0.3);
}

.equalBtn:hover {
  background: linear-gradient(145deg, #1a202c, #0f1419);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.5), 0 6px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(129, 199, 132, 0.3);
}

.operator {
  background: linear-gradient(145deg, #4a5568, #1a202c);
  color: #f48fb1;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(244, 143, 177, 0.2);
  border: 1px solid rgba(244, 143, 177, 0.3);
}

.operator:hover {
  background: linear-gradient(145deg, #1a202c, #0f1419);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.5), 0 6px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(244, 143, 177, 0.3);
}

/* Responsive Design */
@media (max-width: 480px) {
  body {
    padding: 10px;
  }

  .calculator {
    padding: 20px;
    border-radius: 20px;
    max-width: 100%;
  }

  input {
    padding: 20px 16px;
    font-size: clamp(24px, 6vw, 32px);
  }

  button {
    height: 55px;
    min-height: 45px;
    font-size: clamp(14px, 3.5vw, 18px);
    border-radius: 14px;
  }

  .calculator > div {
    gap: 6px;
    margin-bottom: 6px;
  }
}

@media (max-width: 320px) {
  .calculator {
    padding: 15px;
  }

  input {
    padding: 16px 12px;
    margin-bottom: 15px;
  }

  button {
    height: 50px;
    min-height: 40px;
    margin: 2px;
  }

  .calculator > div {
    gap: 4px;
    margin-bottom: 4px;
  }
}

@media (min-width: 768px) {
  .calculator {
    padding: 35px;
  }

  button {
    height: 70px;
  }
}

@media (hover: none) and (pointer: coarse) {
  button:hover {
    transform: none;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  button:active {
    transform: scale(0.95);
  }
}
