@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@500&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.calculator {
  border: none;
  padding: 25px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

input {
  width: 320px;
  border: none;
  padding: 24px;
  margin: 10px;
  background: rgba(248, 249, 250, 0.8);
  border-radius: 12px;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
  font-size: 40px;
  text-align: right;
  cursor: pointer;
  color: #2c3e50;
  font-weight: 500;
}

input::placeholder {
  color: #7f8c8d;
}

button {
  border: none;
  width: 60px;
  height: 60px;
  margin: 10px;
  border-radius: 15px;
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

button:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.equalBtn {
  background: linear-gradient(145deg, #3498db, #2980b9);
  color: white;
}

.equalBtn:hover {
  background: linear-gradient(145deg, #2980b9, #1f5f8b);
}

.operator {
  background: linear-gradient(145deg, #e74c3c, #c0392b);
  color: white;
}

.operator:hover {
  background: linear-gradient(145deg, #c0392b, #a93226);
}
