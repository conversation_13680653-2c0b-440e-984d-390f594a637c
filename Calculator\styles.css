@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@500&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
}

.calculator {
  border: none;
  padding: 30px;
  border-radius: 24px;
  background: linear-gradient(145deg, #3a4a5c 0%, #2c3e50 100%);
  backdrop-filter: blur(15px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.4), 0 15px 30px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.calculator::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(149, 165, 166, 0.4),
    rgba(255, 255, 255, 0.3),
    rgba(149, 165, 166, 0.4),
    transparent
  );
}

input {
  width: 320px;
  border: none;
  padding: 24px 20px;
  margin: 0 0 20px 0;
  background: linear-gradient(145deg, #4a5568, #2d3748);
  border-radius: 16px;
  box-shadow: inset 0 3px 10px rgba(0, 0, 0, 0.3),
    inset 0 1px 3px rgba(0, 0, 0, 0.2), 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 40px;
  text-align: right;
  cursor: pointer;
  color: #e2e8f0;
  font-weight: 500;
  transition: all 0.2s ease;
}

input:focus {
  outline: none;
  box-shadow: inset 0 3px 10px rgba(0, 0, 0, 0.4),
    inset 0 1px 3px rgba(0, 0, 0, 0.3), 0 0 0 3px rgba(149, 165, 166, 0.3),
    0 1px 0 rgba(255, 255, 255, 0.1);
}

input::placeholder {
  color: #a0aec0;
}

button {
  border: none;
  width: 65px;
  height: 65px;
  margin: 8px;
  border-radius: 18px;
  background: linear-gradient(145deg, #4a5568, #2d3748);
  color: #e2e8f0;
  font-size: 20px;
  font-weight: 600;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3), 0 4px 8px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.1);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.4), 0 6px 12px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

button:active {
  transform: translateY(-1px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3), 0 3px 6px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.equalBtn {
  background: linear-gradient(145deg, #4a5568, #1a202c);
  color: #81c784;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(129, 199, 132, 0.2);
  border: 1px solid rgba(129, 199, 132, 0.3);
}

.equalBtn:hover {
  background: linear-gradient(145deg, #1a202c, #0f1419);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.5), 0 6px 12px rgba(0, 0, 0, 0.4),
    inset 0 1px 0 rgba(129, 199, 132, 0.3);
}

.operator {
  background: linear-gradient(145deg, #fd79a8, #e84393);
  color: white;
  box-shadow: 0 8px 16px rgba(253, 121, 168, 0.25),
    0 4px 8px rgba(253, 121, 168, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.operator:hover {
  background: linear-gradient(145deg, #e84393, #d63384);
  box-shadow: 0 10px 20px rgba(253, 121, 168, 0.35),
    0 6px 12px rgba(253, 121, 168, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}
