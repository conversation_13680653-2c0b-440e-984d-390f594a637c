@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@500&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  width: 100%;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background: linear-gradient(135deg, #74b9ff 0%, #0984e3 50%, #6c5ce7 100%);
}

.calculator {
  border: none;
  padding: 30px;
  border-radius: 24px;
  background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
  backdrop-filter: blur(15px);
  box-shadow: 0 25px 50px rgba(116, 185, 255, 0.15),
    0 15px 30px rgba(0, 0, 0, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(116, 185, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.calculator::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(116, 185, 255, 0.6),
    rgba(255, 255, 255, 0.8),
    rgba(108, 92, 231, 0.6),
    transparent
  );
}

input {
  width: 320px;
  border: none;
  padding: 24px 20px;
  margin: 0 0 20px 0;
  background: linear-gradient(145deg, #f8f9fa, #e9ecef);
  border-radius: 16px;
  box-shadow: inset 0 3px 10px rgba(116, 185, 255, 0.1),
    inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(116, 185, 255, 0.1);
  font-size: 40px;
  text-align: right;
  cursor: pointer;
  color: #2d3436;
  font-weight: 500;
  transition: all 0.2s ease;
}

input:focus {
  outline: none;
  box-shadow: inset 0 3px 10px rgba(116, 185, 255, 0.15),
    inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(116, 185, 255, 0.2),
    0 1px 0 rgba(255, 255, 255, 0.9);
}

input::placeholder {
  color: #636e72;
}

button {
  border: none;
  width: 65px;
  height: 65px;
  margin: 8px;
  border-radius: 18px;
  background: linear-gradient(145deg, #ffffff, #f1f3f4);
  color: #2d3436;
  font-size: 20px;
  font-weight: 600;
  box-shadow: 0 8px 16px rgba(116, 185, 255, 0.08),
    0 4px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(116, 185, 255, 0.08);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

button::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  transition: left 0.5s;
}

button:hover::before {
  left: 100%;
}

button:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.12), 0 4px 8px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

button:active {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08), 0 2px 4px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.9);
}

.equalBtn {
  background: linear-gradient(145deg, #00b894, #00a085);
  color: white;
  box-shadow: 0 8px 16px rgba(0, 184, 148, 0.25),
    0 4px 8px rgba(0, 184, 148, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.equalBtn:hover {
  background: linear-gradient(145deg, #00a085, #008f72);
  box-shadow: 0 10px 20px rgba(0, 184, 148, 0.35),
    0 6px 12px rgba(0, 184, 148, 0.25), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.operator {
  background: linear-gradient(145deg, #e74c3c, #c0392b);
  color: white;
  box-shadow: 0 6px 12px rgba(231, 76, 60, 0.3),
    0 2px 4px rgba(231, 76, 60, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.operator:hover {
  background: linear-gradient(145deg, #c0392b, #a93226);
  box-shadow: 0 8px 16px rgba(231, 76, 60, 0.4),
    0 4px 8px rgba(231, 76, 60, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.3);
}
