@import url("https://fonts.googleapis.com/css2?family=Amazon+Ember:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap");

/* CSS Variables */
:root {
  --amazon-orange: #ff9900;
  --amazon-blue: #146eb4;
  --amazon-dark: #232f3e;
  --amazon-light: #37475a;
  --amazon-yellow: #febd69;
  --amazon-red: #b12704;
  --amazon-green: #067d62;

  --text-primary: #0f1111;
  --text-secondary: #565959;
  --text-light: #6f7373;
  --text-white: #ffffff;

  --bg-primary: #ffffff;
  --bg-secondary: #f7f8f8;
  --bg-tertiary: #eaeded;
  --bg-dark: #232f3e;

  --border-light: #d5d9d9;
  --border-medium: #bbbfbf;
  --border-dark: #949494;

  --shadow-light: 0 2px 5px rgba(15, 17, 17, 0.15);
  --shadow-medium: 0 4px 8px rgba(15, 17, 17, 0.15);
  --shadow-heavy: 0 8px 16px rgba(15, 17, 17, 0.15);

  --border-radius: 4px;
  --border-radius-large: 8px;
  --transition: all 0.2s ease;

  --font-amazon: "Amazon Ember", "Roboto", Arial, sans-serif;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-amazon);
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-primary);
  background-color: var(--bg-primary);
}

.container {
  max-width: 1500px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Header Styles */
.header {
  background-color: var(--amazon-dark);
  color: var(--text-white);
  position: sticky;
  top: 0;
  z-index: 1000;
  box-shadow: var(--shadow-medium);
}

.header-top {
  padding: 8px 0;
  border-bottom: 1px solid var(--amazon-light);
}

.header-top .container {
  display: flex;
  align-items: center;
  gap: 20px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo img {
  height: 30px;
  width: auto;
}

.delivery-location {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.delivery-location:hover {
  outline: 1px solid var(--text-white);
}

.delivery-location i {
  color: var(--text-white);
  font-size: 16px;
}

.location-text {
  display: flex;
  flex-direction: column;
}

.deliver-to {
  font-size: 12px;
  color: #cccccc;
}

.location {
  font-size: 14px;
  font-weight: 700;
  color: var(--text-white);
}

/* Search Section */
.search-section {
  flex: 1;
  max-width: 600px;
}

.search-bar {
  display: flex;
  height: 40px;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--shadow-light);
}

.search-category {
  background-color: #f3f3f3;
  border: none;
  padding: 0 10px;
  font-size: 12px;
  color: var(--text-primary);
  cursor: pointer;
  min-width: 50px;
}

.search-input {
  flex: 1;
  border: none;
  padding: 0 12px;
  font-size: 16px;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-light);
}

.search-btn {
  background-color: var(--amazon-orange);
  border: none;
  padding: 0 16px;
  cursor: pointer;
  transition: var(--transition);
}

.search-btn:hover {
  background-color: #e47911;
}

.search-btn i {
  color: var(--text-primary);
  font-size: 16px;
}

/* Header Right */
.header-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.language-selector,
.account-section,
.returns-orders {
  display: flex;
  align-items: center;
  gap: 4px;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-size: 12px;
}

.language-selector:hover,
.account-section:hover,
.returns-orders:hover {
  outline: 1px solid var(--text-white);
}

.account-section {
  flex-direction: column;
  align-items: flex-start;
}

.greeting {
  font-size: 12px;
  color: #cccccc;
}

.account-text {
  font-size: 14px;
  font-weight: 700;
}

.returns-orders {
  flex-direction: column;
  align-items: flex-start;
}

.returns-text {
  font-size: 12px;
  color: #cccccc;
}

.orders-text {
  font-size: 14px;
  font-weight: 700;
}

.cart-section {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  position: relative;
}

.cart-section:hover {
  outline: 1px solid var(--text-white);
}

.cart-section i {
  font-size: 24px;
}

.cart-count {
  position: absolute;
  top: 2px;
  left: 20px;
  background-color: var(--amazon-orange);
  color: var(--text-primary);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 700;
}

.cart-text {
  font-size: 14px;
  font-weight: 700;
}

/* Header Bottom */
.header-bottom {
  background-color: var(--amazon-light);
  padding: 8px 0;
}

.header-bottom .container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.hamburger-menu {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.hamburger-menu:hover {
  outline: 1px solid var(--text-white);
}

.main-nav {
  display: flex;
  align-items: center;
  gap: 20px;
}

.nav-link {
  color: var(--text-white);
  text-decoration: none;
  padding: 8px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  font-size: 14px;
}

.nav-link:hover {
  outline: 1px solid var(--text-white);
}

.nav-link.special {
  color: var(--amazon-yellow);
  font-weight: 700;
}

/* Main Content */
.main-content {
  min-height: calc(100vh - 200px);
}

/* Hero Section */
.hero-section {
  position: relative;
  height: 400px;
  overflow: hidden;
  margin-bottom: 20px;
}

.hero-slider {
  position: relative;
  width: 100%;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.hero-slide.active {
  opacity: 1;
}

.hero-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 80px;
  transform: translateY(-50%);
  color: var(--text-white);
  max-width: 600px;
  z-index: 10;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7), rgba(0, 0, 0, 0.4));
  padding: 40px;
  border-radius: var(--border-radius-large);
  backdrop-filter: blur(10px);
}

.hero-content h2 {
  font-size: 52px;
  font-weight: 700;
  margin-bottom: 20px;
  text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.8);
  line-height: 1.1;
  letter-spacing: -1px;
}

.hero-content p {
  font-size: 20px;
  margin-bottom: 30px;
  text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.8);
  line-height: 1.4;
  opacity: 0.95;
}

.hero-btn {
  background-color: var(--amazon-orange);
  color: var(--text-primary);
  border: none;
  padding: 16px 32px;
  font-size: 18px;
  font-weight: 700;
  border-radius: var(--border-radius-large);
  cursor: pointer;
  transition: var(--transition);
  box-shadow: 0 4px 15px rgba(255, 153, 0, 0.4);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  position: relative;
  overflow: hidden;
}

.hero-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left 0.5s;
}

.hero-btn:hover::before {
  left: 100%;
}

.hero-btn:hover {
  background-color: #e47911;
  transform: translateY(-3px);
  box-shadow: 0 6px 20px rgba(255, 153, 0, 0.6);
}

.hero-controls {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 20px;
  pointer-events: none;
}

.hero-prev,
.hero-next {
  background-color: rgba(255, 255, 255, 0.8);
  border: none;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  pointer-events: all;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-prev:hover,
.hero-next:hover {
  background-color: rgba(255, 255, 255, 1);
  transform: scale(1.1);
}

.hero-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: var(--transition);
}

.indicator.active {
  background-color: var(--amazon-orange);
}

/* Categories Section */
.categories-section {
  padding: 40px 0;
  background-color: var(--bg-secondary);
}

.section-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 30px;
  text-align: center;
  color: var(--text-primary);
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.category-card {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-heavy);
}

.category-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.category-card h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 16px 16px 8px;
  color: var(--text-primary);
}

.category-card p {
  color: var(--text-secondary);
  margin: 0 16px 16px;
  font-size: 14px;
}

.category-btn {
  background-color: var(--amazon-blue);
  color: var(--text-white);
  border: none;
  padding: 10px 20px;
  margin: 0 16px 16px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.category-btn:hover {
  background-color: #0f5a8a;
}

/* Products Section */
.products-section {
  padding: 40px 0;
}

.products-filter {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.filter-btn {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-light);
  padding: 8px 16px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 14px;
  color: var(--text-primary);
}

.filter-btn:hover,
.filter-btn.active {
  background-color: var(--amazon-orange);
  color: var(--text-white);
  border-color: var(--amazon-orange);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.load-more-section {
  text-align: center;
}

.load-more-btn {
  background-color: var(--amazon-blue);
  color: var(--text-white);
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 16px;
  font-weight: 500;
}

.load-more-btn:hover {
  background-color: #0f5a8a;
}

/* Product Card Styles */
.product-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-light);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
}

.product-card:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-2px);
}

.product-image {
  position: relative;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: var(--amazon-red);
  color: var(--text-white);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: 12px;
  font-weight: 600;
}

.product-info {
  padding: 16px;
}

.product-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-primary);
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: var(--amazon-orange);
  font-size: 14px;
}

.star.empty {
  color: var(--border-medium);
}

.rating-count {
  color: var(--text-secondary);
  font-size: 12px;
}

.product-price {
  margin-bottom: 12px;
}

.current-price {
  font-size: 18px;
  font-weight: 700;
  color: var(--amazon-red);
}

.original-price {
  font-size: 14px;
  color: var(--text-secondary);
  text-decoration: line-through;
  margin-left: 8px;
}

.price-discount {
  color: var(--amazon-green);
  font-size: 12px;
  font-weight: 600;
  margin-left: 8px;
}

.product-actions {
  display: flex;
  gap: 8px;
}

.add-to-cart-btn {
  flex: 1;
  background-color: var(--amazon-orange);
  color: var(--text-primary);
  border: none;
  padding: 10px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-weight: 600;
  font-size: 14px;
}

.add-to-cart-btn:hover {
  background-color: #e47911;
}

.wishlist-btn {
  background-color: transparent;
  border: 1px solid var(--border-medium);
  padding: 10px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
}

.wishlist-btn:hover {
  background-color: var(--bg-tertiary);
  color: var(--amazon-red);
}

/* Deals Section */
.deals-section {
  padding: 40px 0;
  background-color: var(--bg-secondary);
}

.deals-grid {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
  align-items: start;
}

.featured-deal {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-light);
  position: relative;
}

.deal-badge {
  position: absolute;
  top: 15px;
  left: 15px;
  background-color: var(--amazon-red);
  color: var(--text-white);
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: 12px;
  font-weight: 700;
  z-index: 2;
}

.featured-deal img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.deal-content {
  padding: 20px;
}

.deal-content h3 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-primary);
}

.deal-price {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.deal-price .current-price {
  font-size: 28px;
  font-weight: 700;
  color: var(--amazon-red);
}

.deal-price .original-price {
  font-size: 18px;
  color: var(--text-secondary);
  text-decoration: line-through;
}

.deal-price .discount {
  background-color: var(--amazon-red);
  color: var(--text-white);
  padding: 4px 8px;
  border-radius: var(--border-radius);
  font-size: 12px;
  font-weight: 700;
}

.deal-timer {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px;
  background-color: var(--bg-secondary);
  border-radius: var(--border-radius);
}

.timer-label {
  font-size: 14px;
  color: var(--text-secondary);
}

.timer-time {
  font-size: 18px;
  font-weight: 700;
  color: var(--amazon-red);
  font-family: "Courier New", monospace;
}

.deal-btn {
  background-color: var(--amazon-orange);
  color: var(--text-primary);
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 16px;
  font-weight: 600;
  width: 100%;
}

.deal-btn:hover {
  background-color: #e47911;
}

.deals-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.deal-item {
  display: flex;
  gap: 12px;
  background-color: var(--bg-primary);
  padding: 12px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-light);
  transition: var(--transition);
  cursor: pointer;
}

.deal-item:hover {
  box-shadow: var(--shadow-medium);
}

.deal-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.deal-info {
  flex: 1;
}

.deal-info h4 {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--text-primary);
}

.deal-info .deal-price {
  margin-bottom: 0;
}

.deal-info .current-price {
  font-size: 16px;
}

.deal-info .discount {
  font-size: 12px;
}

/* Cart Modal */
.cart-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.cart-modal.active {
  opacity: 1;
  visibility: visible;
}

.cart-content {
  background-color: var(--bg-primary);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-heavy);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid var(--border-light);
}

.cart-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-cart {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: var(--text-secondary);
  transition: var(--transition);
}

.close-cart:hover {
  color: var(--text-primary);
}

.cart-body {
  padding: 20px;
  min-height: 200px;
}

.empty-cart {
  text-align: center;
  padding: 40px 20px;
}

.empty-cart i {
  font-size: 48px;
  color: var(--text-light);
  margin-bottom: 16px;
}

.empty-cart p {
  font-size: 16px;
  color: var(--text-secondary);
  margin-bottom: 20px;
}

.continue-shopping {
  background-color: var(--amazon-blue);
  color: var(--text-white);
  border: none;
  padding: 10px 20px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.continue-shopping:hover {
  background-color: #0f5a8a;
}

.cart-item {
  display: flex;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-light);
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.cart-item-info {
  flex: 1;
}

.cart-item-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 4px;
  color: var(--text-primary);
}

.cart-item-price {
  font-size: 16px;
  font-weight: 700;
  color: var(--amazon-red);
  margin-bottom: 8px;
}

.cart-item-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-btn {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-medium);
  width: 30px;
  height: 30px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.quantity-btn:hover {
  background-color: var(--border-light);
}

.quantity {
  font-size: 14px;
  font-weight: 500;
  min-width: 20px;
  text-align: center;
}

.remove-item {
  background: none;
  border: none;
  color: var(--amazon-red);
  cursor: pointer;
  font-size: 12px;
  margin-left: 8px;
  transition: var(--transition);
}

.remove-item:hover {
  text-decoration: underline;
}

.cart-footer {
  padding: 20px;
  border-top: 1px solid var(--border-light);
  background-color: var(--bg-secondary);
}

.cart-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.total-label {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.total-amount {
  font-size: 24px;
  font-weight: 700;
  color: var(--amazon-red);
}

.checkout-btn {
  background-color: var(--amazon-orange);
  color: var(--text-primary);
  border: none;
  padding: 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  font-size: 16px;
  font-weight: 600;
  width: 100%;
}

.checkout-btn:hover {
  background-color: #e47911;
}

/* Footer Styles */
.footer {
  background-color: var(--amazon-dark);
  color: var(--text-white);
  margin-top: 40px;
}

.footer-top {
  background-color: var(--amazon-light);
  text-align: center;
  padding: 15px 0;
}

.back-to-top {
  background: none;
  border: none;
  color: var(--text-white);
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
}

.back-to-top:hover {
  text-decoration: underline;
}

.footer-content {
  padding: 40px 0;
}

.footer-sections {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
}

.footer-section h4 {
  font-size: 16px;
  font-weight: 700;
  margin-bottom: 16px;
  color: var(--text-white);
}

.footer-section ul {
  list-style: none;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #ddd;
  text-decoration: none;
  font-size: 14px;
  transition: var(--transition);
}

.footer-section a:hover {
  text-decoration: underline;
  color: var(--text-white);
}

.footer-bottom {
  background-color: #131a22;
  padding: 20px 0;
  border-top: 1px solid var(--amazon-light);
}

.footer-bottom .container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 20px;
}

.footer-logo img {
  height: 30px;
  width: auto;
}

.footer-links {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.footer-links a {
  color: #ddd;
  text-decoration: none;
  font-size: 12px;
  transition: var(--transition);
}

.footer-links a:hover {
  text-decoration: underline;
  color: var(--text-white);
}

.footer-copyright p {
  color: #ddd;
  font-size: 12px;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .container {
    padding: 0 20px;
  }

  .deals-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .deals-list {
    flex-direction: row;
    overflow-x: auto;
    gap: 15px;
    padding-bottom: 10px;
  }

  .deal-item {
    min-width: 250px;
  }
}

@media (max-width: 992px) {
  .header-top .container {
    flex-wrap: wrap;
    gap: 15px;
  }

  .search-section {
    order: 3;
    width: 100%;
    max-width: none;
  }

  .main-nav {
    display: none;
  }

  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .hero-content h2 {
    font-size: 36px;
  }

  .hero-content p {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .header-top .container {
    flex-direction: column;
    align-items: stretch;
  }

  .header-left {
    justify-content: space-between;
  }

  .header-right {
    justify-content: space-around;
    flex-wrap: wrap;
  }

  .delivery-location {
    display: none;
  }

  .language-selector {
    display: none;
  }

  .hero-content {
    left: 20px;
    right: 20px;
    max-width: calc(100% - 40px);
    padding: 30px 25px;
    text-align: center;
  }

  .hero-content h2 {
    font-size: 32px;
    margin-bottom: 16px;
  }

  .hero-content p {
    font-size: 16px;
    margin-bottom: 25px;
  }

  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
  }

  .products-filter {
    flex-wrap: wrap;
    gap: 8px;
  }

  .filter-btn {
    font-size: 12px;
    padding: 6px 12px;
  }

  .cart-content {
    width: 95%;
    margin: 10px;
  }

  .footer-bottom .container {
    flex-direction: column;
    text-align: center;
  }

  .footer-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 10px;
  }

  .header-top {
    padding: 5px 0;
  }

  .header-bottom {
    padding: 5px 0;
  }

  .logo img {
    height: 25px;
  }

  .search-bar {
    height: 35px;
  }

  .search-category {
    display: none;
  }

  .search-input {
    font-size: 14px;
  }

  .cart-section {
    gap: 4px;
  }

  .cart-section i {
    font-size: 20px;
  }

  .cart-count {
    width: 18px;
    height: 18px;
    font-size: 10px;
    left: 16px;
  }

  .hero-section {
    height: 250px;
  }

  .hero-content {
    left: 15px;
    right: 15px;
    max-width: calc(100% - 30px);
    padding: 25px 20px;
  }

  .hero-content h2 {
    font-size: 24px;
    margin-bottom: 12px;
    line-height: 1.2;
  }

  .hero-content p {
    font-size: 14px;
    margin-bottom: 20px;
    line-height: 1.3;
  }

  .hero-btn {
    padding: 12px 24px;
    font-size: 16px;
    letter-spacing: 0.3px;
  }

  .section-title {
    font-size: 22px;
    margin-bottom: 20px;
  }

  .categories-grid {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .category-card img {
    height: 150px;
  }

  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }

  .product-card .product-image img {
    height: 150px;
  }

  .product-info {
    padding: 12px;
  }

  .product-title {
    font-size: 14px;
  }

  .current-price {
    font-size: 16px;
  }

  .add-to-cart-btn {
    padding: 8px;
    font-size: 12px;
  }

  .deal-content h3 {
    font-size: 18px;
  }

  .deal-price .current-price {
    font-size: 22px;
  }

  .footer-sections {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid var(--border-light);
  border-radius: 50%;
  border-top-color: var(--amazon-orange);
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Smooth Scrolling */
html {
  scroll-behavior: smooth;
}

/* Focus Styles for Accessibility */
button:focus,
input:focus,
select:focus {
  outline: 2px solid var(--amazon-orange);
  outline-offset: 2px;
}

/* Hidden Class */
.hidden {
  display: none !important;
}

/* Fade In Animation */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
