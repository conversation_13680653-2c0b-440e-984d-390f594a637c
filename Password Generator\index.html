<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Password Generator</title>
    <link rel="stylesheet" href="styles.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
      integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
  </head>
  <body>
    <div class="container">
      <h1>Password Generator</h1>

      <!-- password container -->
      <div class="password-container">
        <input type="text" readonly id="password" />
        <i class="far fa-copy" id="copy-btn" title="Copy to clipboard"></i>
      </div>

      <!-- options -->
      <div class="options">
        <!-- password length -->
        <div class="option">
          <label for="length">Password Length</label>
          <div class="range-container">
            <input type="range" id="length" min="6" max="24" value="12" />
            <span id="length-value">12</span>
          </div>
        </div>
        <!-- include uppercase -->
        <div class="option">
          <label for="uppercase">Include Uppercase</label>
          <input type="checkbox" id="uppercase" checked />
        </div>
        <!-- include lowercase -->
        <div class="option">
          <label for="lowercase">Include Lowercase</label>
          <input type="checkbox" id="lowercase" checked />
        </div>
        <!-- include numbers -->
        <div class="option">
          <label for="numbers">Include Numbers</label>
          <input type="checkbox" id="numbers" checked />
        </div>
        <!-- include symbols -->
        <div class="option">
          <label for="symbols">Include Symbols</label>
          <input type="checkbox" id="symbols" checked />
        </div>
      </div>

      <button id="generate-btn">
        <i class="fas fa-key"></i>
        Generate Password
      </button>

      <div class="strength-container">
        <p>Password Strength: <span id="strength-label">Medium</span></p>

        <div class="strength-meter">
          <div class="strength-bar"></div>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
  </body>
</html>
