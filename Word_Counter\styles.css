/* Base styles */
body {
  margin: 0;
  padding: 30px 15px;
  background: #1e1e1e;
  color: #f0f0f0;
  font-family: "Segoe UI", sans-serif;
  display: flex;
  justify-content: center;
}

/* Layout container */
.container {
  max-width: 900px;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Headings */
h1 {
  text-align: center;
  font-size: 2.5rem;
  margin-bottom: 0.2em;
  color: #ffb703;
}

.subtitle {
  text-align: center;
  margin-bottom: 1em;
  font-size: 1rem;
  color: #adb5bd;
}

/* Control section */
.controls {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 1rem;
}

/* Text input area */
textarea {
  width: 100%;
  min-height: 200px;
  padding: 15px;
  font-size: 1rem;
  border-radius: 8px;
  border: none;
  resize: vertical;
  background-color: #2c2c2c;
  color: #fff;
  font-family: sans-serif;
  box-sizing: border-box;
}

/* Buttons */
.action-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin: 15px 0;
}

button {
  padding: 10px 16px;
  font-size: 1rem;
  border: none;
  border-radius: 6px;
  background-color: #4f46e5;
  color: white;
  cursor: pointer;
  transition: background 0.2s ease;
}

button:hover {
  background-color: #4338ca;
}

/* Statistics Table */
.stats-table {
  width: 100%;
  margin-top: 20px;
  border-collapse: collapse;
  background: #2e3440;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
}

.stats-table thead {
  background-color: #3b4252;
}

.stats-table th,
.stats-table td {
  padding: 12px 15px;
  text-align: left;
  border-bottom: 1px solid #444;
  font-size: 1rem;
}

.stats-table tbody tr:hover {
  background-color: #434c5e;
}

.stats-table th {
  color: #fff;
}

.stats-table td {
  color: #e5e9f0;
}

/* Pomodoro Timer */
.pomodoro {
  margin-top: 30px;
  text-align: center;
}

.timer {
  font-size: 2rem;
  margin: 10px 0;
  font-weight: bold;
}

.pomodoro-buttons {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 10px;
}

/* Responsive tweaks */
@media (max-width: 600px) {
  h1 {
    font-size: 2rem;
  }

  .controls {
    flex-direction: column;
    align-items: center;
  }

  button {
    width: 100%;
    max-width: 300px;
  }

  textarea {
    font-size: 1rem;
  }

  .stats-table th,
  .stats-table td {
    font-size: 0.9rem;
  }

  .timer {
    font-size: 1.5rem;
  }
}
