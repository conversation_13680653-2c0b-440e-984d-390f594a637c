* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #66f1e8;
  --primary-dark: #34d3c5;
  --primary-light: #affcf7;
  --bg-dark: #101823;
  --card-bg: rgba(255, 255, 255, 0.05);
  --text-light: #f1f9fa;
  --text-gray: #9ca3af;
  --shadow-md: 0 8px 32px rgba(0, 0, 0, 0.2);
  --border-radius: 12px;
  --glass-blur: blur(14px);
  --glass-border: rgba(255, 255, 255, 0.15);
}

body {
  background: linear-gradient(135deg, #0f2027, #203a43, #2c5364);
  color: var(--text-light);
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 30px 15px;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 800px;
}

header {
  text-align: center;
  margin-bottom: 30px;
}

h1 {
  font-size: 2.2rem;
  margin-bottom: 10px;
  color: var(--primary-color);
}

header p {
  color: var(--text-gray);
  font-size: 1rem;
}

.search-container {
  display: flex;
  margin-bottom: 30px;
  gap: 10px;
}

input[type="text"] {
  flex: 1;
  padding: 12px 16px;
  font-size: 1rem;
  border-radius: var(--border-radius);
  border: none;
  background-color: rgba(255, 255, 255, 0.1);
  color: var(--text-light);
  outline: none;
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
}

input[type="text"]:focus {
  box-shadow: 0 0 0 2px var(--primary-color);
}

button,
.btn {
  background: rgba(102, 241, 232, 0.193);
  color: var(--text-light);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  padding: 10px 20px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  backdrop-filter: var(--glass-blur);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 241, 232, 0.2);
}

button:hover,
.btn:hover {
  background: rgba(102, 241, 232, 0.25);
  box-shadow: 0 6px 18px rgba(102, 241, 232, 0.4);
  transform: translateY(-2px);
}

#profile-container {
  background: var(--card-bg);
  border-radius: var(--border-radius);
  padding: 25px;
  box-shadow: var(--shadow-md);
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
  margin-bottom: 20px;
}

.profile-header {
  display: flex;
  align-items: flex-start;
  gap: 25px;
  margin-bottom: 25px;
}

#avatar {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--primary-color);
}

.profile-info {
  flex: 1;
}

#name {
  font-size: 1.5rem;
  margin-bottom: 5px;
}

#username {
  color: var(--primary-color);
  margin-bottom: 10px;
}

.bio {
  color: var(--text-gray);
  margin-bottom: 15px;
}

.location-date {
  display: flex;
  gap: 20px;
  margin-bottom: 15px;
  color: var(--text-gray);
  font-size: 0.9rem;
}

.location-date i {
  color: var(--primary-color);
  margin-right: 5px;
}

/* Stats */
.stats {
  display: flex;
  gap: 15px;
  justify-content: space-between;
}

.stat {
  background-color: rgba(255, 255, 255, 0.08);
  border-radius: var(--border-radius);
  padding: 12px;
  text-align: center;
  flex: 1;
  backdrop-filter: var(--glass-blur);
  border: 1px solid var(--glass-border);
}

.stat i {
  color: var(--primary-color);
  margin-right: 5px;
}

#error-container {
  background-color: rgba(239, 68, 68, 0.2);
  color: #ef4444;
  padding: 15px;
  border-radius: var(--border-radius);
  text-align: center;
  border: 1px solid rgba(239, 68, 68, 0.4);
}

.hidden {
  display: none;
}

.additional-info {
  margin-top: 25px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 15px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 10px;
  color: var(--text-gray);
  font-size: 0.9rem;
}

.info-item i {
  color: var(--primary-color);
  width: 20px;
  text-align: center;
}

.info-item a {
  color: var(--text-gray);
  text-decoration: none;
  word-break: break-all;
}

.info-item a:hover {
  color: var(--primary-light);
  text-decoration: underline;
}

.repos-section {
  margin-top: 30px;
}

.repos-section h3 {
  font-size: 1.2rem;
  margin-bottom: 15px;
  color: var(--text-light);
  padding-bottom: 10px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.repos-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 15px;
}

.repo-card {
  background-color: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.08);
  transition: transform 0.2s, background-color 0.2s;
  backdrop-filter: var(--glass-blur);
}

.repo-card:hover {
  transform: translateY(-3px);
  background-color: rgba(255, 255, 255, 0.08);
}

.repo-name {
  font-weight: 600;
  margin-bottom: 8px;
  color: var(--primary-light);
  text-decoration: none;
  display: block;
}

.repo-name:hover {
  text-decoration: underline;
}

.repo-description {
  color: var(--text-gray);
  font-size: 0.9rem;
  margin-bottom: 12px;
  overflow: hidden;
  min-height: 35px;
}

.repo-meta {
  display: flex;
  gap: 15px;
  font-size: 0.85rem;
  color: var(--text-gray);
}

.repo-meta-item {
  display: flex;
  align-items: center;
  gap: 5px;
}

.repo-meta-item i {
  color: var(--primary-color);
}

.loading-repos {
  color: var(--text-gray);
  text-align: center;
  padding: 20px;
  grid-column: 1 / -1;
}

.no-repos {
  color: var(--text-gray);
  text-align: center;
  padding: 30px;
  grid-column: 1 / -1;
  border: 1px dashed rgba(255, 255, 255, 0.1);
  border-radius: var(--border-radius);
}

/* Responsive design for mobile devices */
@media (max-width: 600px) {
  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .location-date {
    justify-content: center;
    flex-direction: column;
    gap: 5px;
  }

  .stats,
  .additional-info {
    flex-direction: column;
    grid-template-columns: 1fr;
  }

  .info-item {
    justify-content: center;
  }
}
/* Network Background Canvas */
#network-bg {
  position: fixed;
  top: 0;
  left: 0;
  z-index: -1;
  width: 100vw;
  height: 100vh;
  background: transparent;
  pointer-events: none;
}
