<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Bookmark Saver</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <div class="app-container">
      <h1>Bookmark Saver</h1>
      <div class="input-container">
        <input type="text" id="bookmark-name" placeholder="Bookmark Name" />
        <input type="url" id="bookmark-url" placeholder="Bookmark URL" />
        <select id="bookmark-category">
          <option value="uncategorized">Uncategorized</option>
          <option value="work">Work</option>
          <option value="personal">Personal</option>
          <option value="study">Study</option>
        </select>
        <button id="add-bookmark">Add Bookmark</button>
      </div>
      <div>
        <input
          type="text"
          id="search-bookmarks"
          placeholder="Search bookmarks..."
        />
      </div>
      <div>
        <label>Filter by Category: </label>
        <select id="filter-category">
          <option value="all">All</option>
          <option value="uncategorized">Uncategorized</option>
          <option value="work">Work</option>
          <option value="personal">Personal</option>
          <option value="study">Study</option>
        </select>
      </div>
      <ul id="bookmark-list"></ul>
    </div>
    <script src="script.js"></script>
  </body>
</html>
