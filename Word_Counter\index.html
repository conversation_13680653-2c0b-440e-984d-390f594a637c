<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>The Word Counter</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <div class="container">
      <header>
        <h1>The Word Counter</h1>
        <p class="subtitle">
          Count live characters, words, and more — now with advanced features!
        </p>
      </header>

      <main>
        <section class="controls" aria-label="Editor Settings">
          <label for="fontSize">
            Font Size:
            <select id="fontSize">
              <option value="14px">Small</option>
              <option value="18px" selected>Medium</option>
              <option value="22px">Large</option>
            </select>
          </label>

          <label for="fontStyle">
            Font Style:
            <select id="fontStyle">
              <option value="sans-serif" selected>Sans</option>
              <option value="serif">Serif</option>
              <option value="monospace">Monospace</option>
            </select>
          </label>

          <label for="spellToggle">
            Spell Check:
            <input type="checkbox" id="spellToggle" checked />
          </label>
        </section>

        <section aria-label="Text Editor">
          <textarea
            id="textInput"
            placeholder="Start typing here..."
            spellcheck="true"
            aria-label="Text input area"
          ></textarea>
        </section>

        <section class="action-buttons" aria-label="Editor Actions">
          <button id="copyBtn" aria-label="Copy all text">📋 Copy All</button>
          <button id="pasteBtn" aria-label="Paste text">📥 Paste</button>
          <button id="clearBtn" aria-label="Clear text">🧹 Clear</button>
          <button id="speakBtn" aria-label="Text to Speech">🔊 Speak</button>
          <!-- <button id="translateBtn" aria-label="Translate text">
            🌐 Translate
          </button>
          <button id="detectLangBtn" aria-label="Detect Language">
            🈳 Detect Language
          </button> -->
        </section>

        <section aria-label="Statistics Table">
          <table class="stats-table">
            <thead>
              <tr>
                <th>Metric</th>
                <th>Value</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Characters</td>
                <td id="charCount">0</td>
              </tr>
              <tr>
                <td>Words</td>
                <td id="wordCount">0</td>
              </tr>
              <tr>
                <td>Sentences</td>
                <td id="sentenceCount">0</td>
              </tr>
              <tr>
                <td>Paragraphs</td>
                <td id="paragraphCount">0</td>
              </tr>
              <tr>
                <td>Reading Time</td>
                <td id="readingTime">0 min</td>
              </tr>
              <tr>
                <td>Longest Word</td>
                <td id="longestWord">-</td>
              </tr>
              <tr>
                <td>Shortest Word</td>
                <td id="shortestWord">-</td>
              </tr>
              <tr>
                <td>Avg. Word Length</td>
                <td id="avgWordLength">0</td>
              </tr>
              <tr>
                <td>Vowel Count</td>
                <td id="vowelCount">0</td>
              </tr>
              <tr>
                <td>Consonant Count</td>
                <td id="consonantCount">0</td>
              </tr>
              <tr>
                <td>Most Common Word</td>
                <td id="commonWord">-</td>
              </tr>
              <tr>
                <td>Typing Speed (WPM)</td>
                <td id="typingSpeed">0</td>
              </tr>

              <tr>
                <td>Writing Streak</td>
                <td id="writingStreak">0 days</td>
              </tr>
            </tbody>
          </table>
        </section>

        <section aria-label="Character Frequency Chart">
          <canvas id="charChart" width="800" height="400"></canvas>
        </section>

        <section class="pomodoro" aria-label="Pomodoro Timer">
          <h2>Pomodoro Timer</h2>
          <div class="timer">
            <span id="timerMinutes">25</span>:<span id="timerSeconds">00</span>
          </div>
          <div class="pomodoro-buttons">
            <button id="startPomodoro">▶️ Start</button>
            <button id="pausePomodoro">⏸️ Pause</button>
            <button id="resetPomodoro">🔄 Reset</button>
          </div>
        </section>
      </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="script.js"></script>
  </body>
</html>
