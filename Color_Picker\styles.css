* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", sans-serif;
  background-color: #121212;
  color: #e0e0e0;
  padding: 40px 20px;
}

.container {
  max-width: 960px;
  margin: auto;
  background-color: #1e1e1e;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 0 24px rgba(0, 0, 0, 0.6);
}

h1 {
  font-size: 2.25rem;
  text-align: center;
  margin-bottom: 30px;
  color: #ffffff;
}

.button-group {
  display: flex;
  justify-content: center;
  gap: 20px;
  flex-wrap: wrap;
  margin-bottom: 30px;
}

.button-group button {
  background-color: #2e7dff;
  color: white;
  padding: 14px 30px;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.button-group button:hover {
  background-color: #105dcc;
}

.section {
  display: none;
  margin-top: 30px;
}

.select {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

input[type="color"] {
  width: 120px;
  height: 70px;
  border: none;
  cursor: pointer;
  border-radius: 10px;
  background: none;
  outline: none;
}

.color-picker-layout {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  justify-content: center;
  margin-top: 30px;
}

.selected-display,
.saved-colors-container {
  background-color: #2c2c2c;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.5);
  flex: 1 1 260px;
  max-width: 400px;
}

.color-box.large,
.color-box {
  border: 2px solid #444;
  border-radius: 12px;
}

.color-box.large {
  width: 100px;
  height: 100px;
  margin: 16px 0;
}

.color-box {
  width: 60px;
  height: 60px;
  margin-bottom: 10px;
}

.saved-colors {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 12px;
}

#imagePickerSection h2 {
  text-align: center;
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.color-bar {
  width: 100%;
  max-width: 220px;
  height: 30px;
  border-radius: 8px;
  cursor: pointer;
  border: 1px solid #555;
  transition: transform 0.2s ease, border 0.2s ease;
}

.color-bar:hover {
  transform: scale(1.05);
  border-color: #888;
}

.canvas-input {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 30px;
  margin-top: 20px;
}

input[type="file"] {
  padding: 10px;
  border-radius: 8px;
  background-color: #2c2c2c;
  color: #ccc;
  border: 1px solid #444;
  margin-bottom: 5px;
}

.selected-color-display {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 16px;
  background-color: #252525;
  padding: 16px 24px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

canvas {
  border: 2px solid #444;
  border-radius: 10px;
  max-width: 100%;
  cursor: crosshair;
}

p {
  font-size: 1rem;
  color: #ccc;
  margin-top: 10px;
  text-align: center;
  word-wrap: break-word;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 20px 10px;
  }

  .container {
    padding: 20px;
  }

  h1 {
    font-size: 1.75rem;
  }

  .color-picker-layout {
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  .button-group {
    flex-direction: column;
    align-items: center;
  }

  .canvas-input {
    flex-direction: column;
    gap: 20px;
  }

  .selected-color-display {
    flex-direction: column;
  }

  #imagePickerSection h2 {
    margin-left: 0;
  }
}

@media (max-width: 480px) {
  .color-box.large {
    width: 80px;
    height: 80px;
  }

  .color-box {
    width: 50px;
    height: 50px;
  }

  input[type="color"] {
    width: 100px;
    height: 60px;
  }

  .button-group button {
    width: 100%;
    max-width: 280px;
  }

  .color-bar {
    max-width: 180px;
  }
}

/* Toast Notification */
#toastContainer {
  position: fixed;
  bottom: 24px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
}

.toast {
  background-color: #333;
  color: white;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 0.95rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  animation: fadeInOut 3s ease forwards;
}

@keyframes fadeInOut {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  10% {
    opacity: 1;
    transform: translateY(0);
  }
  90% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-10px);
  }
}

.toast.show {
  opacity: 1;
}
.copyable {
  cursor: pointer;
  text-decoration: underline;
}

.toast {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: #333;
  color: #fff;
  padding: 12px 24px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.4);
  font-weight: bold;
  z-index: 9999;
  animation: fadein 0.3s ease, fadeout 0.3s ease 2.7s;
}

@keyframes fadein {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

@keyframes fadeout {
  from {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  to {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
}
