:root {
  --bg-start: #ffffff;
  --bg-end: #e0e0e0;
  --text-color: #222;
  --accent-color: #6200ee;
}

body.dark {
  --bg-start: #121212;
  --bg-end: #1e1e1e;
  --text-color: #f0f0f0;
  --accent-color: #bb86fc;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", sans-serif;
  background: linear-gradient(135deg, var(--bg-start), var(--bg-end));
  color: var(--text-color);
  transition: background 0.5s ease, color 0.5s ease, opacity 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  overflow: hidden;
  padding: 20px;
}

.container {
  text-align: center;
  max-width: 500px;
  position: relative;
  z-index: 2;
}

/* Theme switch */
.theme-switch {
  position: relative;
  display: inline-block;
  width: 70px;
  height: 34px;
  margin-top: 30px;
}
.theme-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}
.slider {
  background-color: var(--accent-color);
  border-radius: 34px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  cursor: pointer;
  transition: background-color 0.4s;
}
.slider .icon {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  transition: all 0.4s ease;
  font-size: 18px;
}
.sun {
  left: 10px;
  opacity: 1;
}
.moon {
  right: 10px;
  opacity: 0;
}

input:checked + .slider .sun {
  opacity: 0;
  transform: translateY(-50%) rotate(180deg);
}
input:checked + .slider .moon {
  opacity: 1;
  transform: translateY(-50%) rotate(180deg);
}

/* Theme preview thumbnails */
.theme-preview {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 30px;
}
.preview {
  text-align: center;
  cursor: pointer;
  transition: transform 0.3s ease;
}
.preview:hover {
  transform: scale(1.05);
}
.preview img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 0 6px rgba(0, 0, 0, 0.1);
}

/* Animated Background */
.bg-animation {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  background: linear-gradient(-45deg, #ff9a9e, #fad0c4, #fad0c4, #ffdde1);
  background-size: 400% 400%;
  animation: gradientBG 15s ease infinite;
  opacity: 0.3;
}

body.dark .bg-animation {
  background: linear-gradient(-45deg, #1e1e1e, #121212, #292929, #1a1a1a);
}

@keyframes gradientBG {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* Responsive */
@media (max-width: 480px) {
  body {
    padding: 10px;
    text-align: center;
  }
  .theme-preview {
    flex-direction: column;
    gap: 10px;
  }
}
