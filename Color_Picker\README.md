# 🎨 Color Picker App

A clean and responsive web app that lets users pick colors using a visual color picker or directly from an image. Ideal for developers, designers, and creatives who want to build and manage a personal palette of favorite colors with ease.

## 🚀 Features Implemented

1. **Color Picker Tool**  
   A simple color input that lets users select any color and preview it live.

2. **Image Color Picker Tool**  
   Upload an image and click anywhere on it to pick the color from that pixel.

3. **Saved Colors Palette**  
   Save up to 5 favorite colors. Clicking an existing saved color will update it with the currently selected one.

## 🖼 Walkthrough Video

<image src="preview.gif" width="100%" height="100%">

## 💻 How to Use

1. Clone the repository:

```bash
git clone https://github.com/your-username/color-picker-app.git
cd color-picker-app
```

2. Open the `index.html` file in any modern web browser.

> No build step or dependencies required.

## 🛠️ Built With

- HTML5
- CSS3
- JavaScript

## 📌 Folder Structure

```
.
├── index.html         # Main structure
├── styles.css         # App styling
├── script.js          # All logic and interactivity
└── README.md
```

## 📄 License

Made with ❤️ by <PERSON><PERSON><PERSON>.

---

Enjoy using the app! 🎨✨
