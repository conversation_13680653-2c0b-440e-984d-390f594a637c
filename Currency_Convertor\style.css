* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --bg-primary: #f5f7fa;
  --bg-secondary: #ffffff;
  --bg-input: #eef1f6;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  --border-color: #e2e8f0;
  --accent-primary: #4a6cf7;
  --accent-hover: #3a56e4;
  --shadow: rgba(74, 108, 247, 0.08);
}

body {
  background-color: var(--bg-primary);
  font-family: "Poppins", sans-serif;
  color: var(--text-primary);
  min-height: 100vh;
  padding: 20px;
}

/* Three-column layout */
.app-layout {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

/* Make sure each column is properly defined */
#rates-section {
  grid-column: 1 / 2;
}

.main-section {
  grid-column: 2 / 3;
}

#history-section {
  grid-column: 3 / 4;
}

/* Side sections */
.side-section {
  background-color: var(--bg-secondary);
  padding: 20px;
  border-radius: 16px;
  box-shadow: 0 10px 30px var(--shadow);
  height: fit-content;
}

/* Main converter section */
.main-section {
  display: flex;
  justify-content: center;
}

.converter-card {
  background-color: var(--bg-secondary);
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 10px 30px var(--shadow);
  width: 100%;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.header h1 {
  font-size: 1.8rem;
  font-weight: 600;
}

.theme-toggle {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
}

/* Form styling */
.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.9rem;
}

input,
select {
  width: 100%;
  padding: 12px 15px;
  background-color: var(--bg-input);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 1rem;
  color: var(--text-primary);
}

.currency-row {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  margin-bottom: 20px;
}

.from-currency,
.to-currency {
  flex: 1;
  margin-bottom: 0;
}

.swap-btn {
  background-color: var(--bg-input);
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  font-size: 1.2rem;
  margin-bottom: 8px;
}

.convert-btn {
  width: 100%;
  padding: 14px;
  background-color: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.convert-btn:hover {
  background-color: var(--accent-hover);
}

#result {
  margin: 20px 0;
  min-height: 30px;
  text-align: center;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Quick convert section */
.quick-convert {
  margin-top: 30px;
}

.quick-convert h3 {
  margin-bottom: 15px;
  font-size: 1.2rem;
  font-weight: 600;
}

.currency-pairs {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.pair-btn {
  padding: 10px;
  background-color: var(--bg-input);
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.pair-btn:hover {
  background-color: var(--border-color);
}

/* Rates section */
.rates-card h3 {
  margin-bottom: 15px;
  font-size: 1.2rem;
  font-weight: 600;
}

.rates-card table {
  width: 100%;
  border-collapse: collapse;
}

.rates-card th,
.rates-card td {
  padding: 8px;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
  font-size: 0.9rem;
}

.rates-card th {
  font-weight: 600;
  color: var(--text-secondary);
}

/* History section */
.history-card h3 {
  margin-bottom: 15px;
  font-size: 1.2rem;
  font-weight: 600;
}

.history-list {
  list-style: none;
}

.history-list li {
  padding: 10px 0;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-date {
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin-top: 5px;
}

.repeat-btn {
  background: none;
  border: none;
  font-size: 1.2rem;
  cursor: pointer;
  color: var(--accent-primary);
}

/* Hide sections */
.hidden {
  display: none;
}

/* Loading spinner */
.loading-spinner {
  width: 30px;
  height: 30px;
  border: 3px solid var(--bg-input);
  border-top: 3px solid var(--accent-primary);
  border-radius: 50%;
  margin: 0 auto;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Dark theme */
body.dark-theme {
  --bg-primary: #1a202c;
  --bg-secondary: #2d3748;
  --bg-input: #4a5568;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --border-color: #4a5568;
}

/* Responsive layout */
@media (max-width: 1024px) {
  .app-layout {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .side-section,
  .main-section {
    max-width: 100%;
    width: 100%;
  }

  .main-section {
    order: -1; /* Make the converter appear first on smaller screens */
  }

  #rates-section {
    order: 0;
  }

  #history-section {
    order: 1;
  }
}

@media (max-width: 768px) {
  body {
    padding: 15px;
  }

  .converter-card,
  .side-section {
    padding: 20px;
    border-radius: 12px;
  }

  .header h1 {
    font-size: 1.5rem;
  }

  .currency-pairs {
    grid-template-columns: 1fr 1fr;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  body {
    padding: 10px;
  }

  .converter-card,
  .side-section {
    padding: 15px;
    border-radius: 10px;
  }

  .header h1 {
    font-size: 1.3rem;
  }

  .currency-pairs {
    grid-template-columns: 1fr;
  }

  .currency-row {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }

  .swap-btn {
    align-self: center;
    margin: 5px auto;
  }

  input,
  select,
  .convert-btn {
    padding: 10px;
  }

  .rates-card th,
  .rates-card td {
    padding: 6px;
    font-size: 0.8rem;
  }
}
