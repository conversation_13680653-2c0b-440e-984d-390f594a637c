@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap");

/* CSS Variables */
:root {
  --tinder-primary: #fd5068;
  --tinder-secondary: #ff4458;
  --tinder-gold: #ffd700;
  --tinder-blue: #42cdd4;
  --tinder-green: #66d7a2;
  --tinder-purple: #a855f7;

  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #f1f3f4;
  --bg-dark: #1a1a1a;
  --bg-card: #ffffff;
  --bg-overlay: rgba(255, 255, 255, 0.95);

  --text-primary: #2c2c2c;
  --text-secondary: #6c757d;
  --text-light: #adb5bd;
  --text-white: #ffffff;
  --text-dark: #212529;

  --shadow-subtle: 0 1px 3px rgba(0, 0, 0, 0.08);
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 8px 24px rgba(0, 0, 0, 0.15);
  --shadow-card: 0 6px 20px rgba(0, 0, 0, 0.1);

  --border-radius: 16px;
  --border-radius-large: 24px;
  --border-radius-xl: 32px;
  --border-radius-button: 50px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  --font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-xxl: 48px;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: linear-gradient(135deg, #fd5068 0%, #ff4458 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  overflow-x: hidden;
  margin: 0;
}

/* App Container */
.app-container {
  width: 100%;
  max-width: 480px;
  height: 100vh;
  max-height: 900px;
  background: var(--bg-primary);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-heavy);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Header */
.app-header {
  background: var(--bg-primary);
  padding: var(--spacing-xl) var(--spacing-xxl);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 100;
  backdrop-filter: blur(10px);
  min-height: 80px;
  display: flex;
  align-items: center;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-btn {
  width: 52px;
  height: 52px;
  border: none;
  border-radius: 50%;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  box-shadow: var(--shadow-subtle);
  font-size: 18px;
}

.header-btn:hover {
  background: var(--tinder-primary);
  color: var(--text-white);
  transform: scale(1.05);
  box-shadow: var(--shadow-light);
}

.logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  font-size: 32px;
  font-weight: 800;
  color: var(--tinder-primary);
  letter-spacing: -0.5px;
}

.logo i {
  font-size: 36px;
  filter: drop-shadow(0 2px 4px rgba(253, 80, 104, 0.3));
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--tinder-primary);
  color: var(--text-white);
  border-radius: 50%;
  width: 22px;
  height: 22px;
  font-size: 11px;
  font-weight: 700;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(253, 80, 104, 0.4);
  border: 2px solid var(--bg-primary);
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* Card Stack */
.card-stack {
  flex: 1;
  position: relative;
  padding: var(--spacing-xl);
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-card {
  position: absolute;
  width: 100%;
  height: 100%;
  max-height: 600px;
  background: var(--bg-card);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  cursor: grab;
  transition: var(--transition);
  transform-origin: center;
  border: 1px solid rgba(0, 0, 0, 0.04);
}

.profile-card:active {
  cursor: grabbing;
}

.profile-card.dragging {
  transition: none;
}

.profile-card.swiped-right {
  transform: translateX(100%) rotate(30deg);
  opacity: 0;
}

.profile-card.swiped-left {
  transform: translateX(-100%) rotate(-30deg);
  opacity: 0;
}

.profile-card.swiped-up {
  transform: translateY(-100%) scale(1.1);
  opacity: 0;
}

.card-image {
  width: 100%;
  height: 70%;
  position: relative;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.profile-card:hover .card-image img {
  transform: scale(1.05);
}

.card-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  pointer-events: none;
}

.card-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-xl);
  color: var(--text-white);
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
}

.card-name-age {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.card-name {
  font-size: 30px;
  font-weight: 700;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
  letter-spacing: -0.3px;
}

.card-age {
  font-size: 26px;
  font-weight: 500;
  opacity: 0.95;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.5);
}

.card-distance {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
}

.card-bio {
  font-size: 16px;
  line-height: 1.5;
  opacity: 0.95;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.5);
  font-weight: 400;
}

.card-interests {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-md);
}

.interest-tag {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(12px);
  padding: 8px 16px;
  border-radius: var(--border-radius);
  font-size: 13px;
  font-weight: 600;
  border: 1px solid rgba(255, 255, 255, 0.1);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Card Stack Positioning */
.profile-card:nth-child(1) {
  z-index: 3;
  transform: scale(1);
}

.profile-card:nth-child(2) {
  z-index: 2;
  transform: scale(0.95) translateY(10px);
  opacity: 0.8;
}

.profile-card:nth-child(3) {
  z-index: 1;
  transform: scale(0.9) translateY(20px);
  opacity: 0.6;
}

/* No More Cards */
.no-more-cards {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xxl);
  text-align: center;
}

.no-cards-content {
  max-width: 320px;
}

.no-cards-content i {
  font-size: 72px;
  color: var(--text-light);
  margin-bottom: var(--spacing-lg);
  opacity: 0.7;
}

.no-cards-content h3 {
  font-size: 26px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
  letter-spacing: -0.3px;
}

.no-cards-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xl);
  line-height: 1.6;
  font-size: 16px;
}

.reload-btn {
  background: linear-gradient(
    135deg,
    var(--tinder-primary),
    var(--tinder-secondary)
  );
  color: var(--text-white);
  border: none;
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-button);
  font-weight: 700;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin: 0 auto;
  box-shadow: var(--shadow-medium);
  letter-spacing: 0.3px;
}

.reload-btn:hover {
  background: linear-gradient(135deg, var(--tinder-secondary), #e63946);
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-xl);
  background: var(--bg-primary);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

.action-btn {
  width: 70px;
  height: 70px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26px;
  box-shadow: var(--shadow-medium);
  position: relative;
  overflow: hidden;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

.action-btn::before {
  content: "";
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.2) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: var(--transition);
}

.action-btn:hover::before {
  opacity: 1;
}

.action-btn:active {
  transform: scale(0.92);
}

.pass-btn {
  background: linear-gradient(135deg, #ffffff, #f8f9fa);
  color: #dc3545;
  border: 2px solid #dc3545;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.15);
}

.pass-btn:hover {
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: var(--text-white);
  border-color: #dc3545;
  transform: scale(1.08);
  box-shadow: 0 6px 20px rgba(220, 53, 69, 0.3);
}

.super-like-btn {
  background: linear-gradient(135deg, var(--tinder-blue), #2bb8c4);
  color: var(--text-white);
  width: 60px;
  height: 60px;
  font-size: 22px;
  box-shadow: 0 4px 12px rgba(66, 205, 212, 0.3);
}

.super-like-btn:hover {
  background: linear-gradient(135deg, #2bb8c4, #1fa8b3);
  transform: scale(1.08);
  box-shadow: 0 6px 20px rgba(66, 205, 212, 0.4);
}

.like-btn {
  background: linear-gradient(135deg, var(--tinder-green), #28a745);
  color: var(--text-white);
  box-shadow: 0 4px 12px rgba(66, 215, 66, 0.3);
}

.like-btn:hover {
  background: linear-gradient(135deg, #28a745, #218838);
  transform: scale(1.08);
  box-shadow: 0 6px 20px rgba(66, 215, 66, 0.4);
}

.boost-btn {
  background: linear-gradient(135deg, var(--tinder-purple), #9333ea);
  color: var(--text-white);
  width: 60px;
  height: 60px;
  font-size: 22px;
  box-shadow: 0 4px 12px rgba(168, 85, 247, 0.3);
}

.boost-btn:hover {
  background: linear-gradient(135deg, #9333ea, #7c3aed);
  transform: scale(1.08);
  box-shadow: 0 6px 20px rgba(168, 85, 247, 0.4);
}

.rewind-btn {
  background: linear-gradient(135deg, var(--tinder-gold), #f59e0b);
  color: var(--text-white);
  width: 60px;
  height: 60px;
  font-size: 22px;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.rewind-btn:hover {
  background: linear-gradient(135deg, #f59e0b, #d97706);
  transform: scale(1.08);
  box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

/* Swipe Indicators */
.swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: var(--bg-overlay);
  backdrop-filter: blur(16px);
  padding: var(--spacing-lg) var(--spacing-xl);
  border-radius: var(--border-radius-large);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 800;
  font-size: 20px;
  opacity: 0;
  transition: var(--transition);
  pointer-events: none;
  z-index: 10;
  box-shadow: var(--shadow-medium);
  letter-spacing: 0.5px;
}

.like-indicator {
  right: var(--spacing-xl);
  color: var(--tinder-green);
  border: 3px solid var(--tinder-green);
  background: rgba(66, 215, 66, 0.1);
}

.pass-indicator {
  left: var(--spacing-xl);
  color: #dc3545;
  border: 3px solid #dc3545;
  background: rgba(220, 53, 69, 0.1);
}

.swipe-indicator.show {
  opacity: 1;
  transform: translateY(-50%) scale(1.05);
}

/* Super Like Animation */
.super-like-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--tinder-blue);
  color: var(--text-white);
  padding: 20px 30px;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  font-size: 18px;
  opacity: 0;
  pointer-events: none;
  z-index: 20;
}

.super-like-animation i {
  font-size: 32px;
}

.super-like-animation.show {
  animation: superLikeAnimation 1s ease-out;
}

@keyframes superLikeAnimation {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Match Modal */
.match-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.match-modal.show {
  opacity: 1;
  visibility: visible;
}

.match-content {
  background: linear-gradient(
    135deg,
    var(--tinder-primary),
    var(--tinder-secondary)
  );
  padding: var(--spacing-xxl) var(--spacing-xxl);
  border-radius: var(--border-radius-xl);
  text-align: center;
  max-width: 420px;
  width: 90%;
  color: var(--text-white);
  transform: scale(0.8);
  transition: var(--transition);
  box-shadow: 0 20px 40px rgba(253, 80, 104, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.match-modal.show .match-content {
  transform: scale(1);
}

.match-header h2 {
  font-size: 36px;
  font-weight: 800;
  margin-bottom: var(--spacing-sm);
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: -0.5px;
}

.match-header p {
  font-size: 18px;
  opacity: 0.95;
  margin-bottom: var(--spacing-xl);
  font-weight: 500;
}

.match-photos {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-xl);
}

.match-photo {
  width: 90px;
  height: 90px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--text-white);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  position: relative;
}

.match-photo::after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 50%;
  box-shadow: inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.match-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.match-heart {
  font-size: 28px;
  color: var(--text-white);
  animation: heartBeat 1s infinite;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

@keyframes heartBeat {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

.match-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.match-btn {
  padding: var(--spacing-md) var(--spacing-xl);
  border: none;
  border-radius: var(--border-radius-button);
  font-weight: 700;
  font-size: 16px;
  cursor: pointer;
  transition: var(--transition);
  letter-spacing: 0.3px;
  box-shadow: var(--shadow-medium);
}

.keep-swiping {
  background: rgba(255, 255, 255, 0.15);
  color: var(--text-white);
  border: 2px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.keep-swiping:hover {
  background: var(--text-white);
  color: var(--tinder-primary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

.send-message {
  background: var(--text-white);
  color: var(--tinder-primary);
  font-weight: 800;
}

.send-message:hover {
  background: var(--bg-secondary);
  transform: translateY(-2px);
  box-shadow: var(--shadow-heavy);
}

/* Profile Modal */
.profile-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.profile-modal.show {
  opacity: 1;
  visibility: visible;
}

.profile-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  max-width: 480px;
  width: 90%;
  max-height: 85vh;
  overflow-y: auto;
  transform: translateY(50px);
  transition: var(--transition);
}

.profile-modal.show .profile-content {
  transform: translateY(0);
}

.profile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.profile-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-secondary);
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--tinder-primary);
  color: var(--text-white);
}

.profile-body {
  padding: 20px;
}

.profile-photo-section {
  text-align: center;
  margin-bottom: 24px;
}

.profile-photo {
  position: relative;
  display: inline-block;
}

.profile-photo img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--bg-secondary);
}

.edit-photo-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36px;
  height: 36px;
  background: var(--tinder-primary);
  color: var(--text-white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-photo-btn:hover {
  background: var(--tinder-secondary);
  transform: scale(1.1);
}

.info-group {
  margin-bottom: 20px;
}

.info-group label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.info-group input,
.info-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-size: 14px;
  transition: var(--transition);
  background: var(--bg-secondary);
}

.info-group input:focus,
.info-group textarea:focus {
  outline: none;
  border-color: var(--tinder-primary);
}

.info-group textarea {
  resize: vertical;
  min-height: 80px;
}

.interests-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.interest-tag {
  background: var(--tinder-primary);
  color: var(--text-white);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.profile-settings {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
  margin-top: 20px;
}

.setting-group label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  display: block;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.range-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.range-container input[type="range"] {
  width: 100px;
}

.range-value,
.age-display {
  font-weight: 600;
  color: var(--tinder-primary);
  min-width: 60px;
}

.age-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.age-range input[type="range"] {
  width: 80px;
}

/* Chat Modal */
.chat-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  backdrop-filter: blur(8px);
}

.chat-modal.show {
  opacity: 1;
  visibility: visible;
}

.chat-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  max-width: 480px;
  width: 90%;
  height: 85vh;
  display: flex;
  flex-direction: column;
  transform: translateY(50px);
  transition: var(--transition);
  box-shadow: var(--shadow-heavy);
  border: 1px solid rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.chat-modal.show .chat-content {
  transform: translateY(0);
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-xl) var(--spacing-xxl);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: var(--bg-primary);
  min-height: 80px;
}

.chat-header h3 {
  font-size: 24px;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.3px;
}

.back-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-secondary);
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:hover {
  background: var(--tinder-primary);
  color: var(--text-white);
}

.chat-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.matches-list {
  padding: var(--spacing-xl) var(--spacing-xxl);
  overflow-y: auto;
  flex: 1;
  background: var(--bg-secondary);
}

.matches-list h4 {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  letter-spacing: -0.2px;
}

.matches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-subtle);
}

.match-item {
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
  padding: var(--spacing-sm);
  border-radius: var(--border-radius);
}

.match-item:hover {
  transform: scale(1.05);
  background: var(--bg-secondary);
}

.match-item img {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--tinder-primary);
  margin-bottom: var(--spacing-sm);
  box-shadow: var(--shadow-light);
}

.match-item span {
  font-size: 13px;
  font-weight: 600;
  color: var(--text-primary);
  display: block;
  letter-spacing: -0.1px;
}

.conversations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-subtle);
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
  border: 1px solid transparent;
}

.conversation-item:hover {
  background: var(--bg-secondary);
  border-color: rgba(253, 80, 104, 0.1);
  transform: translateX(4px);
}

.conversation-item img {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--bg-secondary);
  box-shadow: var(--shadow-light);
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-name {
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  font-size: 16px;
  letter-spacing: -0.2px;
}

.conversation-preview {
  font-size: 14px;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.4;
}

.conversation-time {
  font-size: 12px;
  color: var(--text-light);
  font-weight: 500;
  white-space: nowrap;
}

.unread-indicator {
  width: 10px;
  height: 10px;
  background: var(--tinder-primary);
  border-radius: 50%;
  box-shadow: 0 0 0 2px var(--bg-primary);
}

/* Chat Conversation */
.chat-conversation {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.conversation-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-xxl);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  background: var(--bg-primary);
  min-height: 80px;
  box-shadow: var(--shadow-subtle);
}

.chat-user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
}

.chat-user-info img {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid var(--bg-secondary);
  box-shadow: var(--shadow-light);
}

.chat-user-details h4 {
  font-size: 18px;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  letter-spacing: -0.2px;
}

.online-status {
  font-size: 13px;
  color: var(--tinder-green);
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.online-status::before {
  content: "";
  width: 8px;
  height: 8px;
  background: var(--tinder-green);
  border-radius: 50%;
  display: inline-block;
}

.chat-options-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-options-btn:hover {
  color: var(--tinder-primary);
}

.messages-container {
  flex: 1;
  padding: var(--spacing-xl);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  background: linear-gradient(to bottom, #fafbfc, #f8f9fa);
  scroll-behavior: smooth;
}

.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.message {
  max-width: 75%;
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: 20px;
  font-size: 15px;
  line-height: 1.5;
  position: relative;
  word-wrap: break-word;
  box-shadow: var(--shadow-subtle);
}

.message.sent {
  align-self: flex-end;
  background: linear-gradient(
    135deg,
    var(--tinder-primary),
    var(--tinder-secondary)
  );
  color: var(--text-white);
  border-bottom-right-radius: 8px;
}

.message.received {
  align-self: flex-start;
  background: var(--bg-primary);
  color: var(--text-primary);
  border-bottom-left-radius: 8px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: var(--spacing-xs);
  font-weight: 500;
}

.message-input-container {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-lg) var(--spacing-xxl);
  border-top: 1px solid rgba(0, 0, 0, 0.08);
  background: var(--bg-primary);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.message-input-container input {
  flex: 1;
  padding: var(--spacing-md) var(--spacing-lg);
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-radius: 25px;
  font-family: var(--font-family);
  font-size: 15px;
  transition: var(--transition);
  background: var(--bg-secondary);
  min-height: 48px;
}

.message-input-container input:focus {
  outline: none;
  border-color: var(--tinder-primary);
  background: var(--bg-primary);
  box-shadow: 0 0 0 3px rgba(253, 80, 104, 0.1);
}

.message-input-container input::placeholder {
  color: var(--text-light);
  font-weight: 500;
}

.send-btn {
  width: 48px;
  height: 48px;
  background: linear-gradient(
    135deg,
    var(--tinder-primary),
    var(--tinder-secondary)
  );
  color: var(--text-white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  box-shadow: var(--shadow-light);
}

.send-btn:hover {
  background: linear-gradient(135deg, var(--tinder-secondary), #e63946);
  transform: scale(1.05);
  box-shadow: var(--shadow-medium);
}

.send-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  inset: 0;
  background: linear-gradient(
    135deg,
    var(--tinder-primary),
    var(--tinder-secondary)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  transition: var(--transition);
}

.loading-screen.hide {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: var(--text-white);
}

.loading-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 30px;
}

.loading-logo i {
  font-size: 48px;
  animation: logoFlame 2s ease-in-out infinite;
}

@keyframes logoFlame {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(5deg);
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--text-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-content p {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 500;
}

/* Large Desktop Improvements */
@media (min-width: 1200px) {
  .app-container {
    max-width: 520px;
    max-height: 950px;
  }

  .profile-card {
    max-height: 650px;
  }

  .card-name {
    font-size: 34px;
  }

  .card-age {
    font-size: 28px;
  }

  .card-bio {
    font-size: 17px;
    -webkit-line-clamp: 3;
  }

  .action-btn {
    width: 75px;
    height: 75px;
    font-size: 28px;
  }

  .super-like-btn,
  .boost-btn,
  .rewind-btn {
    width: 65px;
    height: 65px;
    font-size: 24px;
  }

  .match-content {
    max-width: 460px;
    padding: 60px 50px;
  }

  .profile-content,
  .chat-content {
    max-width: 520px;
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  .app-container {
    max-width: 100%;
    height: 100vh;
    border-radius: 0;
  }

  .app-header {
    padding: var(--spacing-lg) var(--spacing-xl);
    min-height: 70px;
  }

  .header-btn {
    width: 48px;
    height: 48px;
    font-size: 16px;
  }

  .logo {
    font-size: 28px;
  }

  .logo i {
    font-size: 32px;
  }

  .card-stack {
    padding: var(--spacing-lg);
  }

  .profile-card {
    max-height: 520px;
  }

  .card-info {
    padding: var(--spacing-xl);
  }

  .card-name {
    font-size: 26px;
  }

  .card-age {
    font-size: 22px;
  }

  .card-bio {
    font-size: 15px;
  }

  .action-buttons {
    padding: var(--spacing-lg);
    gap: var(--spacing-md);
  }

  .action-btn {
    width: 60px;
    height: 60px;
    font-size: 22px;
  }

  .super-like-btn,
  .boost-btn,
  .rewind-btn {
    width: 52px;
    height: 52px;
    font-size: 18px;
  }

  .match-content {
    padding: var(--spacing-xxl) var(--spacing-lg);
    max-width: 360px;
  }

  .match-header h2 {
    font-size: 32px;
  }

  .match-photos {
    gap: var(--spacing-lg);
  }

  .match-photo {
    width: 80px;
    height: 80px;
  }

  .profile-content,
  .chat-content {
    width: 95%;
    max-width: 100%;
  }

  .chat-content {
    height: 90vh;
  }

  .profile-content {
    max-height: 90vh;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 12px 16px;
  }

  .header-btn {
    width: 36px;
    height: 36px;
  }

  .logo {
    font-size: 18px;
  }

  .logo i {
    font-size: 20px;
  }

  .card-stack {
    padding: 12px;
  }

  .card-info {
    padding: 12px;
  }

  .card-name {
    font-size: 18px;
  }

  .card-age {
    font-size: 16px;
  }

  .card-bio {
    font-size: 13px;
  }

  .action-buttons {
    padding: 12px;
    gap: 8px;
  }

  .action-btn {
    width: 44px;
    height: 44px;
    font-size: 16px;
  }

  .super-like-btn,
  .boost-btn,
  .rewind-btn {
    width: 36px;
    height: 36px;
    font-size: 12px;
  }

  .swipe-indicator {
    padding: 12px 16px;
    font-size: 14px;
  }

  .like-indicator {
    right: 12px;
  }

  .pass-indicator {
    left: 12px;
  }

  .match-content {
    padding: 24px 16px;
    max-width: 280px;
  }

  .match-header h2 {
    font-size: 24px;
  }

  .match-header p {
    font-size: 14px;
  }

  .match-photos {
    gap: 12px;
    margin-bottom: 24px;
  }

  .match-photo {
    width: 60px;
    height: 60px;
  }

  .match-heart {
    font-size: 20px;
  }

  .loading-logo {
    font-size: 28px;
    margin-bottom: 24px;
  }

  .loading-logo i {
    font-size: 36px;
  }

  .loading-content p {
    font-size: 14px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #3a3a3a;
    --bg-card: #2a2a2a;

    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-light: #808080;
    --text-dark: #ffffff;
  }

  .profile-content,
  .chat-content {
    background: var(--bg-primary);
    color: var(--text-primary);
  }

  .message.received {
    background: var(--bg-tertiary);
    color: var(--text-primary);
  }

  .info-group input,
  .info-group textarea,
  .message-input-container input {
    background: var(--bg-tertiary);
    border-color: #404040;
    color: var(--text-primary);
  }

  .conversation-item:hover {
    background: var(--bg-tertiary);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .action-btn {
    border: 2px solid currentColor;
  }

  .profile-card {
    border: 2px solid var(--text-primary);
  }

  .swipe-indicator {
    border-width: 4px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .loading-logo i {
    animation: none;
  }

  .loading-spinner {
    animation: none;
    border: 4px solid var(--text-white);
  }

  .match-heart {
    animation: none;
  }
}
