@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800&display=swap");

/* CSS Variables */
:root {
  --tinder-primary: #fd5068;
  --tinder-secondary: #ff4458;
  --tinder-gold: #ffd700;
  --tinder-blue: #42cdd4;
  --tinder-green: #66d7a2;
  --tinder-purple: #a855f7;

  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-dark: #1a1a1a;
  --bg-card: #ffffff;

  --text-primary: #424242;
  --text-secondary: #757575;
  --text-light: #9e9e9e;
  --text-white: #ffffff;
  --text-dark: #212121;

  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
  --shadow-card: 0 8px 25px rgba(0, 0, 0, 0.12);

  --border-radius: 12px;
  --border-radius-large: 20px;
  --border-radius-xl: 30px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-fast: all 0.2s ease;

  --font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, sans-serif;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  background: linear-gradient(135deg, #fd5068 0%, #ff4458 100%);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  overflow-x: hidden;
}

/* App Container */
.app-container {
  width: 100%;
  max-width: 400px;
  height: 100vh;
  max-height: 800px;
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-heavy);
  overflow: hidden;
  position: relative;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: var(--bg-primary);
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
  position: relative;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.header-btn:hover {
  background: var(--tinder-primary);
  color: var(--text-white);
  transform: scale(1.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 24px;
  font-weight: 800;
  color: var(--tinder-primary);
}

.logo i {
  font-size: 28px;
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: var(--tinder-primary);
  color: var(--text-white);
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Main Content */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* Card Stack */
.card-stack {
  flex: 1;
  position: relative;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profile-card {
  position: absolute;
  width: 100%;
  height: 100%;
  max-height: 500px;
  background: var(--bg-card);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-card);
  overflow: hidden;
  cursor: grab;
  transition: var(--transition);
  transform-origin: center;
}

.profile-card:active {
  cursor: grabbing;
}

.profile-card.dragging {
  transition: none;
}

.profile-card.swiped-right {
  transform: translateX(100%) rotate(30deg);
  opacity: 0;
}

.profile-card.swiped-left {
  transform: translateX(-100%) rotate(-30deg);
  opacity: 0;
}

.profile-card.swiped-up {
  transform: translateY(-100%) scale(1.1);
  opacity: 0;
}

.card-image {
  width: 100%;
  height: 70%;
  position: relative;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.profile-card:hover .card-image img {
  transform: scale(1.05);
}

.card-gradient {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 50%;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  pointer-events: none;
}

.card-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  color: var(--text-white);
}

.card-name-age {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.card-name {
  font-size: 24px;
  font-weight: 700;
}

.card-age {
  font-size: 20px;
  font-weight: 400;
  opacity: 0.9;
}

.card-distance {
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 8px;
}

.card-bio {
  font-size: 14px;
  line-height: 1.4;
  opacity: 0.9;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.card-interests {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 12px;
}

.interest-tag {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

/* Card Stack Positioning */
.profile-card:nth-child(1) {
  z-index: 3;
  transform: scale(1);
}

.profile-card:nth-child(2) {
  z-index: 2;
  transform: scale(0.95) translateY(10px);
  opacity: 0.8;
}

.profile-card:nth-child(3) {
  z-index: 1;
  transform: scale(0.9) translateY(20px);
  opacity: 0.6;
}

/* No More Cards */
.no-more-cards {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
}

.no-cards-content {
  max-width: 300px;
}

.no-cards-content i {
  font-size: 64px;
  color: var(--text-light);
  margin-bottom: 20px;
}

.no-cards-content h3 {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.no-cards-content p {
  color: var(--text-secondary);
  margin-bottom: 24px;
  line-height: 1.5;
}

.reload-btn {
  background: var(--tinder-primary);
  color: var(--text-white);
  border: none;
  padding: 12px 24px;
  border-radius: var(--border-radius);
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 auto;
}

.reload-btn:hover {
  background: var(--tinder-secondary);
  transform: translateY(-2px);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--bg-primary);
}

.action-btn {
  width: 56px;
  height: 56px;
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
}

.action-btn::before {
  content: "";
  position: absolute;
  inset: 0;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.3) 0%,
    transparent 70%
  );
  opacity: 0;
  transition: var(--transition);
}

.action-btn:hover::before {
  opacity: 1;
}

.action-btn:active {
  transform: scale(0.95);
}

.pass-btn {
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 2px solid #e0e0e0;
}

.pass-btn:hover {
  background: #ff6b6b;
  color: var(--text-white);
  border-color: #ff6b6b;
  transform: scale(1.1);
}

.super-like-btn {
  background: var(--tinder-blue);
  color: var(--text-white);
  width: 48px;
  height: 48px;
  font-size: 16px;
}

.super-like-btn:hover {
  background: #2bb8c4;
  transform: scale(1.1);
}

.like-btn {
  background: var(--tinder-green);
  color: var(--text-white);
}

.like-btn:hover {
  background: #4caf50;
  transform: scale(1.1);
}

.boost-btn {
  background: var(--tinder-purple);
  color: var(--text-white);
  width: 48px;
  height: 48px;
  font-size: 16px;
}

.boost-btn:hover {
  background: #9333ea;
  transform: scale(1.1);
}

.rewind-btn {
  background: var(--tinder-gold);
  color: var(--text-white);
  width: 48px;
  height: 48px;
  font-size: 16px;
}

.rewind-btn:hover {
  background: #f59e0b;
  transform: scale(1.1);
}

/* Swipe Indicators */
.swipe-indicator {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  font-size: 18px;
  opacity: 0;
  transition: var(--transition);
  pointer-events: none;
  z-index: 10;
}

.like-indicator {
  right: 20px;
  color: var(--tinder-green);
  border: 3px solid var(--tinder-green);
}

.pass-indicator {
  left: 20px;
  color: #ff6b6b;
  border: 3px solid #ff6b6b;
}

.swipe-indicator.show {
  opacity: 1;
  transform: translateY(-50%) scale(1.1);
}

/* Super Like Animation */
.super-like-animation {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--tinder-blue);
  color: var(--text-white);
  padding: 20px 30px;
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  font-weight: 700;
  font-size: 18px;
  opacity: 0;
  pointer-events: none;
  z-index: 20;
}

.super-like-animation i {
  font-size: 32px;
}

.super-like-animation.show {
  animation: superLikeAnimation 1s ease-out;
}

@keyframes superLikeAnimation {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.5);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* Match Modal */
.match-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.match-modal.show {
  opacity: 1;
  visibility: visible;
}

.match-content {
  background: linear-gradient(
    135deg,
    var(--tinder-primary),
    var(--tinder-secondary)
  );
  padding: 40px 30px;
  border-radius: var(--border-radius-xl);
  text-align: center;
  max-width: 350px;
  width: 90%;
  color: var(--text-white);
  transform: scale(0.8);
  transition: var(--transition);
}

.match-modal.show .match-content {
  transform: scale(1);
}

.match-header h2 {
  font-size: 32px;
  font-weight: 800;
  margin-bottom: 8px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.match-header p {
  font-size: 16px;
  opacity: 0.9;
  margin-bottom: 30px;
}

.match-photos {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.match-photo {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid var(--text-white);
  box-shadow: var(--shadow-medium);
}

.match-photo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.match-heart {
  font-size: 24px;
  color: var(--text-white);
  animation: heartBeat 1s infinite;
}

@keyframes heartBeat {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
}

.match-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.match-btn {
  padding: 14px 24px;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 600;
  font-size: 16px;
  cursor: pointer;
  transition: var(--transition);
}

.keep-swiping {
  background: rgba(255, 255, 255, 0.2);
  color: var(--text-white);
  border: 2px solid var(--text-white);
}

.keep-swiping:hover {
  background: var(--text-white);
  color: var(--tinder-primary);
}

.send-message {
  background: var(--text-white);
  color: var(--tinder-primary);
}

.send-message:hover {
  background: var(--bg-secondary);
  transform: translateY(-2px);
}

/* Profile Modal */
.profile-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.profile-modal.show {
  opacity: 1;
  visibility: visible;
}

.profile-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  transform: translateY(50px);
  transition: var(--transition);
}

.profile-modal.show .profile-content {
  transform: translateY(0);
}

.profile-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.profile-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-secondary);
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: var(--tinder-primary);
  color: var(--text-white);
}

.profile-body {
  padding: 20px;
}

.profile-photo-section {
  text-align: center;
  margin-bottom: 24px;
}

.profile-photo {
  position: relative;
  display: inline-block;
}

.profile-photo img {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  object-fit: cover;
  border: 4px solid var(--bg-secondary);
}

.edit-photo-btn {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 36px;
  height: 36px;
  background: var(--tinder-primary);
  color: var(--text-white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-photo-btn:hover {
  background: var(--tinder-secondary);
  transform: scale(1.1);
}

.info-group {
  margin-bottom: 20px;
}

.info-group label {
  display: block;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.info-group input,
.info-group textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: var(--border-radius);
  font-family: var(--font-family);
  font-size: 14px;
  transition: var(--transition);
  background: var(--bg-secondary);
}

.info-group input:focus,
.info-group textarea:focus {
  outline: none;
  border-color: var(--tinder-primary);
}

.info-group textarea {
  resize: vertical;
  min-height: 80px;
}

.interests-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.interest-tag {
  background: var(--tinder-primary);
  color: var(--text-white);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

.profile-settings {
  border-top: 1px solid #f0f0f0;
  padding-top: 20px;
  margin-top: 20px;
}

.setting-group label {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
  display: block;
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.range-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.range-container input[type="range"] {
  width: 100px;
}

.range-value,
.age-display {
  font-weight: 600;
  color: var(--tinder-primary);
  min-width: 60px;
}

.age-range {
  display: flex;
  align-items: center;
  gap: 8px;
}

.age-range input[type="range"] {
  width: 80px;
}

/* Chat Modal */
.chat-modal {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
}

.chat-modal.show {
  opacity: 1;
  visibility: visible;
}

.chat-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  max-width: 400px;
  width: 90%;
  height: 80vh;
  display: flex;
  flex-direction: column;
  transform: translateY(50px);
  transition: var(--transition);
}

.chat-modal.show .chat-content {
  transform: translateY(0);
}

.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.chat-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.back-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: var(--bg-secondary);
  border-radius: 50%;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn:hover {
  background: var(--tinder-primary);
  color: var(--text-white);
}

.chat-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.matches-list {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

.matches-list h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 16px;
}

.matches-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 12px;
  margin-bottom: 24px;
}

.match-item {
  text-align: center;
  cursor: pointer;
  transition: var(--transition);
}

.match-item:hover {
  transform: scale(1.05);
}

.match-item img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  border: 3px solid var(--tinder-primary);
  margin-bottom: 4px;
}

.match-item span {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
  display: block;
}

.conversations-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.conversation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: var(--transition);
}

.conversation-item:hover {
  background: var(--bg-secondary);
}

.conversation-item img {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  object-fit: cover;
}

.conversation-info {
  flex: 1;
}

.conversation-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.conversation-preview {
  font-size: 14px;
  color: var(--text-secondary);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.conversation-time {
  font-size: 12px;
  color: var(--text-light);
}

.unread-indicator {
  width: 8px;
  height: 8px;
  background: var(--tinder-primary);
  border-radius: 50%;
}

/* Chat Conversation */
.chat-conversation {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.conversation-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.chat-user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.chat-user-info img {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
}

.chat-user-details h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.online-status {
  font-size: 12px;
  color: var(--tinder-green);
  font-weight: 500;
}

.chat-options-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.chat-options-btn:hover {
  color: var(--tinder-primary);
}

.messages-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message {
  max-width: 80%;
  padding: 12px 16px;
  border-radius: 18px;
  font-size: 14px;
  line-height: 1.4;
  position: relative;
}

.message.sent {
  align-self: flex-end;
  background: var(--tinder-primary);
  color: var(--text-white);
  border-bottom-right-radius: 6px;
}

.message.received {
  align-self: flex-start;
  background: var(--bg-secondary);
  color: var(--text-primary);
  border-bottom-left-radius: 6px;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
  margin-top: 4px;
}

.message-input-container {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  border-top: 1px solid #f0f0f0;
}

.message-input-container input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 25px;
  font-family: var(--font-family);
  font-size: 14px;
  transition: var(--transition);
}

.message-input-container input:focus {
  outline: none;
  border-color: var(--tinder-primary);
}

.send-btn {
  width: 40px;
  height: 40px;
  background: var(--tinder-primary);
  color: var(--text-white);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-btn:hover {
  background: var(--tinder-secondary);
  transform: scale(1.1);
}

.send-btn:disabled {
  background: var(--text-light);
  cursor: not-allowed;
  transform: none;
}

/* Loading Screen */
.loading-screen {
  position: fixed;
  inset: 0;
  background: linear-gradient(
    135deg,
    var(--tinder-primary),
    var(--tinder-secondary)
  );
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  transition: var(--transition);
}

.loading-screen.hide {
  opacity: 0;
  visibility: hidden;
}

.loading-content {
  text-align: center;
  color: var(--text-white);
}

.loading-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  font-size: 36px;
  font-weight: 800;
  margin-bottom: 30px;
}

.loading-logo i {
  font-size: 48px;
  animation: logoFlame 2s ease-in-out infinite;
}

@keyframes logoFlame {
  0%,
  100% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(1.1) rotate(5deg);
  }
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid var(--text-white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-content p {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  .app-container {
    max-width: 100%;
    height: 100vh;
    border-radius: 0;
  }

  .app-header {
    padding: 16px 20px;
  }

  .logo {
    font-size: 20px;
  }

  .logo i {
    font-size: 24px;
  }

  .card-stack {
    padding: 16px;
  }

  .card-info {
    padding: 16px;
  }

  .card-name {
    font-size: 20px;
  }

  .card-age {
    font-size: 18px;
  }

  .action-buttons {
    padding: 16px;
    gap: 12px;
  }

  .action-btn {
    width: 48px;
    height: 48px;
    font-size: 18px;
  }

  .super-like-btn,
  .boost-btn,
  .rewind-btn {
    width: 40px;
    height: 40px;
    font-size: 14px;
  }

  .match-content {
    padding: 30px 20px;
    max-width: 320px;
  }

  .match-header h2 {
    font-size: 28px;
  }

  .match-photos {
    gap: 16px;
  }

  .match-photo {
    width: 70px;
    height: 70px;
  }

  .profile-content,
  .chat-content {
    width: 95%;
    max-width: 100%;
  }

  .chat-content {
    height: 90vh;
  }

  .profile-content {
    max-height: 90vh;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 12px 16px;
  }

  .header-btn {
    width: 36px;
    height: 36px;
  }

  .logo {
    font-size: 18px;
  }

  .logo i {
    font-size: 20px;
  }

  .card-stack {
    padding: 12px;
  }

  .card-info {
    padding: 12px;
  }

  .card-name {
    font-size: 18px;
  }

  .card-age {
    font-size: 16px;
  }

  .card-bio {
    font-size: 13px;
  }

  .action-buttons {
    padding: 12px;
    gap: 8px;
  }

  .action-btn {
    width: 44px;
    height: 44px;
    font-size: 16px;
  }

  .super-like-btn,
  .boost-btn,
  .rewind-btn {
    width: 36px;
    height: 36px;
    font-size: 12px;
  }

  .swipe-indicator {
    padding: 12px 16px;
    font-size: 14px;
  }

  .like-indicator {
    right: 12px;
  }

  .pass-indicator {
    left: 12px;
  }

  .match-content {
    padding: 24px 16px;
    max-width: 280px;
  }

  .match-header h2 {
    font-size: 24px;
  }

  .match-header p {
    font-size: 14px;
  }

  .match-photos {
    gap: 12px;
    margin-bottom: 24px;
  }

  .match-photo {
    width: 60px;
    height: 60px;
  }

  .match-heart {
    font-size: 20px;
  }

  .loading-logo {
    font-size: 28px;
    margin-bottom: 24px;
  }

  .loading-logo i {
    font-size: 36px;
  }

  .loading-content p {
    font-size: 14px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2a2a2a;
    --bg-tertiary: #3a3a3a;
    --bg-card: #2a2a2a;

    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --text-light: #808080;
    --text-dark: #ffffff;
  }

  .profile-content,
  .chat-content {
    background: var(--bg-primary);
    color: var(--text-primary);
  }

  .message.received {
    background: var(--bg-tertiary);
    color: var(--text-primary);
  }

  .info-group input,
  .info-group textarea,
  .message-input-container input {
    background: var(--bg-tertiary);
    border-color: #404040;
    color: var(--text-primary);
  }

  .conversation-item:hover {
    background: var(--bg-tertiary);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .action-btn {
    border: 2px solid currentColor;
  }

  .profile-card {
    border: 2px solid var(--text-primary);
  }

  .swipe-indicator {
    border-width: 4px;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .loading-logo i {
    animation: none;
  }

  .loading-spinner {
    animation: none;
    border: 4px solid var(--text-white);
  }

  .match-heart {
    animation: none;
  }
}
