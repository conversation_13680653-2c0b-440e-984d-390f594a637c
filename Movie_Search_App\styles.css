/* Base Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  --primary-color: #6c5ce7;
  --secondary-color: #a29bfe;
  --accent-color: #fd79a8;
  --dark-color: #2d3436;
  --light-color: #f8f9fa;
  --text-color: #333;
  --light-text: #f8f9fa;
  --shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

body {
  font-family: "Poppins", sans-serif;
  background-color: var(--light-color);
  color: var(--text-color);
  line-height: 1.6;
}

a {
  text-decoration: none;
  color: inherit;
}

ul {
  list-style: none;
}

/* Header */
header {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: 8px;
}

nav ul {
  display: flex;
  gap: 30px;
}

nav a {
  font-weight: 500;
  padding: 8px 0;
  position: relative;
  transition: var(--transition);
}

nav a:hover,
nav a.active {
  color: var(--primary-color);
}

nav a::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--primary-color);
  transition: var(--transition);
}

nav a:hover::after,
nav a.active::after {
  width: 100%;
}

/* Hero Section */
.hero {
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  padding: 80px 20px;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 40px;
  opacity: 0.9;
}

.search-container {
  position: relative;
  display: flex;
  max-width: 600px;
  margin: 0 auto;
  box-shadow: var(--shadow);
  border-radius: 50px;
  overflow: hidden;
}

#search-input {
  flex: 1;
  padding: 18px 25px;
  border: none;
  font-size: 16px;
  font-family: inherit;
}

#search-input:focus {
  outline: none;
}

#search-button {
  background-color: white;
  color: var(--primary-color);
  border: none;
  padding: 0 30px;
  cursor: pointer;
  font-size: 18px;
  transition: var(--transition);
}

#search-button:hover {
  background-color: var(--accent-color);
  color: white;
}

/* Typing indicator */
.typing-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: var(--primary-color);
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  opacity: 0.8;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* Main Container */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 60px 20px;
  overflow-x: hidden;
}

/* Filters */
.filters {
  background-color: white;
  padding: 20px;
  border-radius: 10px;
  box-shadow: var(--shadow);
  margin-bottom: 40px;
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-group label {
  font-weight: 500;
}

.filter-group select {
  padding: 10px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-family: inherit;
  background-color: white;
}

#filter-button {
  margin-left: auto;
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
}

#filter-button:hover {
  background-color: var(--secondary-color);
}

/* Results Container */
#results-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.movie-card {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  cursor: pointer;
}

.movie-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.15);
}

.movie-poster {
  width: 100%;
  height: 375px;
  object-fit: cover;
}

.movie-info {
  padding: 20px;
}

.movie-title {
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--dark-color);
}

.movie-meta {
  display: flex;
  justify-content: space-between;
  color: #777;
  font-size: 14px;
}

.movie-year,
.movie-language {
  display: flex;
  align-items: center;
  gap: 5px;
}

.movie-rating {
  display: flex;
  align-items: center;
  margin-top: 10px;
  gap: 5px;
}

.movie-rating i {
  color: #f1c40f;
}

/* No results message */
#results-container p {
  grid-column: 1 / -1;
  text-align: center;
  padding: 40px;
  background-color: white;
  border-radius: 10px;
  box-shadow: var(--shadow);
  font-size: 16px;
  color: #777;
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.pagination button {
  background-color: white;
  border: 1px solid #ddd;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination button:not(:disabled):hover {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

#page-info {
  font-weight: 500;
}

/* Modal Styles */
/* Remove scrollbars from main page */
html,
body {
  overflow-x: hidden;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

/* Hide scrollbar for Chrome, Safari and Opera */
html::-webkit-scrollbar,
body::-webkit-scrollbar {
  display: none;
}

/* Modal Styles - keep scrolling functionality but hide scrollbar */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  overflow: auto;
}

/* Style scrollbar for Chrome, Safari and Opera in modal */
.modal::-webkit-scrollbar {
  width: 8px; /* Width of the scrollbar */
  display: block; /* Show scrollbar */
}

.modal::-webkit-scrollbar-track {
  background: transparent; /* Track background */
}

.modal::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3); /* Thumb color */
  border-radius: 4px; /* Rounded corners */
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  width: 90%;
  max-width: 900px;
  border-radius: 10px;
  position: relative;
  animation: modalFadeIn 0.3s;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  max-height: 90vh; /* Limit height to 90% of viewport height */
  overflow-y: auto; /* Allow scrolling within modal content */
}

/* Style scrollbar for modal content */
.modal-content::-webkit-scrollbar {
  width: 8px;
  display: block;
}

.modal-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 0 10px 10px 0;
}

.modal-content::-webkit-scrollbar-thumb {
  background-color: #888;
  border-radius: 4px;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background-color: #555;
}

/* Ensure content doesn't cause horizontal scrolling */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 60px 20px;
  overflow-x: hidden;
}

/* Modal body with scrollable content */
.modal-body {
  padding: 30px;
  overflow-y: auto;
}

.modal-header {
  padding: 30px;
  background-size: cover;
  background-position: center;
  position: relative;
  display: flex;
  align-items: flex-end;
  min-height: 300px;
}

.modal-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.9), transparent);
}

.modal-poster {
  position: relative;
  z-index: 1;
  margin-right: 30px;
}

.modal-poster img {
  width: 150px;
  border-radius: 5px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.modal-title {
  position: relative;
  z-index: 1;
  color: white;
  flex: 1;
}

.modal-title h2 {
  font-size: 32px;
  margin-bottom: 10px;
}

.modal-meta {
  opacity: 0.8;
  margin-bottom: 15px;
}

.modal-rating {
  display: inline-flex;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 5px 10px;
  border-radius: 20px;
}

.modal-rating i {
  color: #f1c40f;
  margin-right: 5px;
}

.modal-body {
  padding: 30px;
}

.modal-overview {
  font-size: 16px;
  line-height: 1.7;
  margin-bottom: 30px;
}

.modal-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.modal-details p {
  margin-bottom: 10px;
}

.modal-trailer h3 {
  margin-bottom: 15px;
  font-size: 20px;
}

.video-container {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  border-radius: 10px;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.close-modal {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 28px;
  cursor: pointer;
  color: white;
  z-index: 10;
  background-color: rgba(0, 0, 0, 0.5);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: var(--transition);
}

.close-modal:hover {
  background-color: var(--accent-color);
}

/* Movie card enhancements */
.movie-card {
  position: relative;
  overflow: hidden;
}

.movie-info {
  padding: 20px;
}

.movie-title {
  font-size: 18px;
  margin-bottom: 10px;
  font-weight: 600;
  color: var(--dark-color);
}

.movie-year,
.movie-language {
  margin-bottom: 5px;
  font-size: 14px;
  color: #777;
}

.movie-rating {
  display: flex;
  align-items: center;
  margin-top: 10px;
  gap: 5px;
}

.movie-rating i {
  color: #f1c40f;
}

/* Loading animation enhancement */
.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  grid-column: 1 / -1;
}

.loading::after {
  content: "";
  width: 50px;
  height: 50px;
  border: 5px solid #f3f3f3;
  border-top: 5px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive adjustments for modal */
@media (max-width: 768px) {
  .modal-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 20px;
  }

  .modal-poster {
    margin-right: 0;
    margin-bottom: 20px;
  }

  .modal-poster img {
    width: 120px;
  }

  .modal-title h2 {
    font-size: 24px;
  }

  .modal-body {
    padding: 20px;
  }

  .modal-details {
    grid-template-columns: 1fr;
  }
}

/* Section Styles */
.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

.section-title {
  font-size: 2rem;
  margin-bottom: 30px;
  color: var(--primary-color);
  text-align: center;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: var(--accent-color);
}

/* Popular Section */
.category-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
  gap: 10px;
}

.category-tab {
  background-color: white;
  border: 1px solid #ddd;
  padding: 10px 20px;
  border-radius: 30px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.category-tab.active {
  background-color: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

/* Categories Section */
.genres-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.genre-card {
  background-color: white;
  border-radius: 10px;
  padding: 30px 20px;
  text-align: center;
  box-shadow: var(--shadow);
  cursor: pointer;
  transition: var(--transition);
}

.genre-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
  background-color: var(--primary-color);
  color: white;
}

.genre-card i {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: var(--accent-color);
}

.genre-card:hover i {
  color: white;
}

.genre-card h3 {
  font-size: 1.2rem;
  font-weight: 600;
}

/* About Section */
.about-content {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: var(--shadow);
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0;
}

.about-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.about-text {
  padding: 40px;
}

.about-text h3 {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--primary-color);
}

.about-text p {
  margin-bottom: 20px;
  line-height: 1.7;
}

.about-text ul {
  margin-bottom: 30px;
}

.about-text li {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.about-text li i {
  color: var(--accent-color);
  font-size: 1.2rem;
}

.tmdb-attribution {
  display: flex;
  align-items: center;
  gap: 20px;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  margin-top: 30px;
}

.tmdb-attribution img {
  width: 60px;
}

.tmdb-attribution p {
  margin-bottom: 0;
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .about-content {
    grid-template-columns: 1fr;
  }

  .about-image {
    height: 200px;
  }

  .about-text {
    padding: 30px;
  }

  .category-tabs {
    flex-wrap: wrap;
  }

  .genres-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }
}

/* Footer */
footer {
  background-color: var(--dark-color);
  color: var(--light-text);
  padding: 60px 20px 20px;
}

.footer-content {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h3 {
  font-size: 20px;
  margin-bottom: 20px;
  position: relative;
  padding-bottom: 10px;
}

.footer-section h3::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--accent-color);
}

.footer-section p {
  margin-bottom: 20px;
  opacity: 0.8;
}

.footer-section ul li {
  margin-bottom: 10px;
}

.footer-section ul li a {
  opacity: 0.8;
  transition: var(--transition);
}

.footer-section ul li a:hover {
  opacity: 1;
  color: var(--accent-color);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  display: inline-block;
  width: 40px;
  height: 40px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: var(--transition);
}

.social-icons a:hover {
  background-color: var(--accent-color);
  transform: translateY(-5px);
}

.footer-bottom {
  max-width: 1400px;
  margin: 0 auto;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 14px;
  opacity: 0.7;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    gap: 15px;
  }

  nav ul {
    gap: 15px;
  }

  .hero h1 {
    font-size: 2.2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .search-container {
    flex-direction: column;
    border-radius: 10px;
  }

  #search-input {
    border-radius: 10px 10px 0 0;
  }

  #search-button {
    border-radius: 0 0 10px 10px;
    padding: 15px;
  }

  .filters {
    flex-direction: column;
    align-items: stretch;
  }

  #filter-button {
    margin-left: 0;
    width: 100%;
  }

  #results-container {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }

  .movie-poster {
    height: 240px;
  }

  .modal-content {
    width: 95%;
    padding: 20px;
    margin: 10% auto;
  }
}

/* Dark Mode Styles */
body.dark-mode {
  background-color: var(--dark-color);
  color: var(--light-text);
}

body.dark-mode header {
  background-color: #1a1a1a;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

body.dark-mode .filters,
body.dark-mode .movie-card,
body.dark-mode .about-content,
body.dark-mode .genre-card,
body.dark-mode .category-tab {
  background-color: #2a2a2a;
  color: var(--light-text);
}

body.dark-mode .filter-group select {
  background-color: #333;
  color: var(--light-text);
  border-color: #444;
}

body.dark-mode .movie-title {
  color: var(--light-text);
}

body.dark-mode .tmdb-attribution {
  background-color: #333;
}

/* Dark mode toggle button */
.dark-mode-toggle {
  display: flex;
  align-items: center;
  margin-left: 20px;
  cursor: pointer;
}

.dark-mode-toggle i {
  font-size: 1.2rem;
  margin-right: 5px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 20px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: var(--transition);
  border-radius: 20px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: var(--transition);
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--primary-color);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

/* Watchlist Button */
.watchlist-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  border: none;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: var(--transition);
  z-index: 10;
}

.watchlist-btn:hover {
  background-color: var(--accent-color);
}

.watchlist-btn.active {
  background-color: var(--accent-color);
}

/* Watchlist Section */
#watchlist-section {
  padding-top: 30px;
}

.watchlist-empty {
  text-align: center;
  padding: 40px;
  background-color: white;
  border-radius: 10px;
  box-shadow: var(--shadow);
}

.watchlist-empty i {
  font-size: 3rem;
  color: var(--accent-color);
  margin-bottom: 20px;
}

.watchlist-empty p {
  margin-bottom: 20px;
}

.watchlist-empty button {
  background-color: var(--primary-color);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: var(--transition);
}

.watchlist-empty button:hover {
  background-color: var(--secondary-color);
}

/* Toast Notifications */
.toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.toast {
  background-color: var(--dark-color);
  color: white;
  padding: 12px 20px;
  border-radius: 5px;
  margin-top: 10px;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  animation: slideIn 0.3s, fadeOut 0.5s 2.5s forwards;
  max-width: 300px;
}

.toast i {
  margin-right: 10px;
  font-size: 1.2rem;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
