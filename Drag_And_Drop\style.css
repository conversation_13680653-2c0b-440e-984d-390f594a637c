* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  font-family: sans-serif;
  background-color: #f4f4f4;
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.container {
  text-align: center;
  width: 100%;
  padding: 1rem;
}

h1 {
  color: #333;
  margin-bottom: 20px;
  font-size: 2rem;
}

.board {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  gap: 20px;
}

.list {
  background-color: #e3e4e8;
  padding: 1rem;
  border-radius: 8px;
  width: 30%;
  min-height: 400px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  position: relative;
}

.list h2 {
  color: #555;
  margin-bottom: 0.5rem;
}

.card-container {
  min-height: 300px;
}

.card {
  background-color: white;
  color: #333;
  padding: 0.8rem 1rem;
  margin: 0.5rem 0;
  border-radius: 6px;
  cursor: grab;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease-in-out;
}

.card input {
  border: none;
  font-size: 1rem;
  width: 90%;
}

.card .delete-btn {
  cursor: pointer;
  font-weight: bold;
  margin-left: 10px;
  color: red;
}

.card:active {
  cursor: grabbing;
}

.add-btn {
  margin-top: 10px;
  padding: 8px 12px;
  border: none;
  background-color: #333;
  color: #fff;
  border-radius: 6px;
  cursor: pointer;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #ccc;
  border-radius: 4px;
  margin-bottom: 8px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  width: 0%;
  background-color: #28a745;
  transition: width 0.3s ease-in-out;
}

@media (max-width: 768px) {
  .board {
    flex-direction: column;
    align-items: center;
  }
  .list {
    width: 90%;
  }
}
