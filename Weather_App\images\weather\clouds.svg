<svg width="101" height="100" viewBox="0 0 101 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_186_67)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M33.4941 34.3313C41.8558 34.3376 48.6281 41.077 48.6217 49.3849C48.6153 57.6928 41.8324 64.4216 33.4707 64.4152C25.1089 64.4089 18.3366 57.6695 18.343 49.3616C18.3494 41.0537 25.1324 34.325 33.4941 34.3313Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.8843 69.4353C29.8843 69.6171 29.8608 69.7989 29.8119 69.9828L27.2991 79.2695C26.9949 80.3941 25.8311 81.06 24.6991 80.7577C23.5651 80.4554 22.8949 79.2991 23.1991 78.1744L25.7119 68.8878C26.0162 67.7631 27.18 67.0972 28.3119 67.3974C29.2608 67.6511 29.8843 68.5051 29.8843 69.4353Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3677 65.6228C19.3677 66.1915 19.1379 66.758 18.6826 67.1724L11.5655 73.6897C10.7038 74.4783 9.36129 74.4233 8.56767 73.5671C7.77193 72.711 7.82725 71.3771 8.68895 70.5864L15.806 64.0712C16.6698 63.2827 18.0124 63.3355 18.806 64.1938C19.1826 64.5976 19.3677 65.1112 19.3677 65.6228Z" fill="#F9C900"/>
<mask id="mask0_186_67" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="53" width="14" height="7">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 53.2717V59.8971H13.616V53.2717L0 53.2717L0 53.2717Z" fill="white"/>
</mask>
<g mask="url(#mask0_186_67)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.616 55.3837C13.616 56.3223 12.9798 57.1764 12.0245 57.4216L2.65426 59.8294C1.52022 60.1212 0.360642 59.4426 0.0670254 58.3158C-0.224464 57.1891 0.456387 56.037 1.59256 55.7452L10.9628 53.3395C12.0968 53.0478 13.2543 53.7243 13.5479 54.8531C13.5947 55.0307 13.616 55.2083 13.616 55.3837Z" fill="#F9C900"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M13.9138 43.5692C13.9138 43.7425 13.8925 43.918 13.8457 44.0955C13.5543 45.2223 12.3968 45.9009 11.2628 45.6112L1.88829 43.214C0.754251 42.9244 0.0712725 41.7723 0.362762 40.6455C0.654251 39.5188 1.8117 38.8402 2.94787 39.1298L12.3202 41.5271C13.2777 41.7723 13.9138 42.6284 13.9138 43.5692Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.3153 33.3004C19.3153 33.8289 19.1175 34.3574 18.7175 34.7654C17.9026 35.6025 16.5579 35.6237 15.7153 34.814L8.76427 28.1233C7.92172 27.3137 7.90044 25.9798 8.71533 25.1426C9.53023 24.3055 10.8749 24.2843 11.7175 25.094L18.6685 31.7826C19.1004 32.1969 19.3153 32.7487 19.3153 33.3004Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M29.1759 27.4368C29.1759 28.3797 28.5334 29.2379 27.5717 29.4789C26.4355 29.7643 25.2802 29.0794 24.9951 27.9484L22.6291 18.6237C22.344 17.4927 23.0334 16.349 24.1696 16.0637C25.3057 15.7804 26.4611 16.4653 26.7462 17.5942L29.1121 26.9189C29.1547 27.0922 29.1759 27.2656 29.1759 27.4368Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M43.3853 18.7993C43.3853 18.9769 43.3619 19.1566 43.3151 19.3363L40.8555 28.6357C40.5576 29.7624 39.3959 30.4346 38.2619 30.1387C37.13 29.8427 36.4513 28.6885 36.7491 27.5639L39.2087 18.2624C39.5066 17.1378 40.6683 16.4634 41.8023 16.7594C42.7555 17.0088 43.3853 17.8629 43.3853 18.7993Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M58.7764 26.8291C58.7764 27.4168 58.5296 28.0024 58.0466 28.4188L50.7445 34.7311C49.8615 35.4943 48.5189 35.4013 47.7508 34.524C46.9806 33.6446 47.0742 32.3127 47.9593 31.5475L55.2615 25.2373C56.1445 24.472 57.4849 24.565 58.2551 25.4445C58.6062 25.844 58.7764 26.3387 58.7764 26.8291Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M66.3019 40.903C66.3019 41.8289 65.6828 42.6766 64.7402 42.9345L55.4083 45.4776C54.2764 45.7863 53.1083 45.1267 52.7998 44.0021C52.4891 42.8774 53.153 41.719 54.2849 41.4103L63.6168 38.8672C64.7487 38.5586 65.9147 39.2181 66.2253 40.3428C66.2764 40.5288 66.3019 40.7169 66.3019 40.903Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M66.1415 57.3443C66.1415 57.5176 66.1202 57.6931 66.0734 57.8707C65.7819 58.9974 64.6245 59.676 63.4904 59.3864L54.116 56.9892C52.9819 56.6995 52.2968 55.5495 52.5904 54.4207C52.8819 53.2939 54.0394 52.6153 55.1755 52.905L64.5479 55.3022C65.5053 55.5474 66.1415 56.4036 66.1415 57.3443Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M58.2155 71.8243C58.2155 72.3718 58.0028 72.9172 57.5772 73.3294C56.7389 74.1454 55.3943 74.1306 54.5751 73.2977L47.8092 66.4209C46.99 65.588 47.0049 64.2541 47.8432 63.4403C48.6815 62.6243 50.0262 62.6391 50.8453 63.472L57.6113 70.3487C58.0155 70.7588 58.2155 71.2915 58.2155 71.8243Z" fill="#F9C900"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M43.6032 79.601C43.6032 80.5312 42.9798 81.381 42.033 81.6347C40.9011 81.9369 39.7351 81.271 39.4309 80.1464L36.9117 70.8619C36.6075 69.7372 37.2777 68.5809 38.4096 68.2765C39.5415 67.9742 40.7075 68.6401 41.0117 69.7647L43.5287 79.0493C43.5798 79.2332 43.6032 79.4192 43.6032 79.601Z" fill="#F9C900"/>
<mask id="mask1_186_67" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="18" y="35" width="82" height="48">
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.1161 82.2365H99.1778V35.9773H18.1161V82.2365Z" fill="white"/>
</mask>
<g mask="url(#mask1_186_67)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M30.3927 57.599C23.6247 57.599 18.1161 63.1249 18.1161 69.9192C18.1161 76.7093 23.6247 82.2373 30.3927 82.2373H83.7693C92.2672 82.2373 99.1778 75.2993 99.1778 66.7715C99.1778 58.2437 92.2672 51.3057 83.7693 51.3057C82.1885 51.3057 80.6161 51.5509 79.1013 52.0308L78.114 52.3458L77.7651 51.3649C74.5034 42.1607 65.7842 35.9773 56.0715 35.9773C44.197 35.9773 34.3417 44.9067 33.1459 56.7471L33.0353 57.8442L31.9459 57.7089C31.3417 57.6328 30.8481 57.599 30.3927 57.599Z" fill="white"/>
</g>
</g>
<defs>
<clipPath id="clip0_186_67">
<rect width="101" height="67" fill="white" transform="translate(0 16)"/>
</clipPath>
</defs>
</svg>
