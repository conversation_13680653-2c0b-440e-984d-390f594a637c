<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Responsive Theme Toggle</title>
    <link
      id="favicon"
      rel="icon"
      type="image/x-icon"
      href="favicon-light.ico"
    />
    <link rel="stylesheet" href="style.css" />
    <script defer src="script.js"></script>
  </head>
  <body>
    <div class="bg-animation"></div>

    <div class="container">
      <h1>Welcome to Theme Toggle Page</h1>
      <p>
        This page supports light and dark themes with animation, persistence,
        and background gradient.
      </p>

      <!-- Theme Toggle Switch -->
      <label class="theme-switch" id="themeToggle">
        <input
          type="checkbox"
          id="toggleCheckbox"
          aria-label="Toggle Dark Mode"
        />
        <span class="slider">
          <i class="icon sun">☀️</i>
          <i class="icon moon">🌙</i>
        </span>
      </label>

      <!-- Theme Preview Thumbnails -->
      <div class="theme-preview">
        <div class="preview" id="lightPreview">
          <img src="light.png" alt="Light Theme Preview" />
          <p>Light</p>
        </div>
        <div class="preview" id="darkPreview">
          <img src="dark.png" alt="Dark Theme Preview" />
          <p>Dark</p>
        </div>
      </div>
    </div>
  </body>
</html>
