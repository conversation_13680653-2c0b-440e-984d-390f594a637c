<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Quiz App</title>
    <link rel="stylesheet" href="styles.css" />
    <!-- Linking Google Fonts for icons -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0" />
  </head>
  <body>
    <!-- Quiz configuration container -->
    <div class="config-container">
      <h2 class="config-title">Quiz Configuration</h2>
       <!-- which type of category -->
      <div class="config-options">
        <h4 class="option-title">Select category</h4>
        <div class="category-options">
          <button class="category-option active">Programming</button>
          <button class="category-option">Geography</button>
          <button class="category-option">Mathematics</button>
          <button class="category-option">Entertainment</button>
        </div>
      </div>
        <!-- No. of questions -->
        <div class="config-options">
          <h4 class="option-title">No. of Questions</h4>
          <div class="question-options">
            <button class="question-option">5</button>
            <button class="question-option active">10</button>
            <button class="question-option">15</button>
            <button class="question-option">20</button>
            <button class="question-option">25</button>
          </div>
        </div>

        <button class="start-quiz-btn">Start Quiz</button>
      </div>
    </div>

    <!-- Quiz questions display container -->
     <div class="quiz-container">
      <header class="quiz-header">
        <h2 class="quiz-title">Quiz Application</h2>
        <div class="quiz-timer">
          <span class="material-symbols-rounded">
            timer
            </span>
          <p class="time-duration">15s</p>
        </div>
      </header>
      <div class="quiz-content">
        <h1 class="question-text"></h1>
        <ul class="answer-options">
          
        </ul>
      </div>
      <div class="quiz-footer">
        <div class="question-status"></div>
        <button class="next-qsn-btn">Next <span class="material-symbols-rounded">
         arrow_right_alt
          </span></button>
      </div>
     </div>

     <!-- Result container -->
      <div class="result-container">
        <img src="images/quiz-over.png" class="result-img">
        <h2 class="result-title">Quiz Completed!</h2>
        <p class="result-message"></p>
        <button class="try-again-btn">Try Again</button>
      </div>
      <script src="javascript/questions.js"></script>
    <script src="javascript/script.js"></script>
   
  </body>
</html>
