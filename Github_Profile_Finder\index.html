<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Github User Finder</title>
    <link rel="stylesheet" href="style.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css"
      integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg=="
      crossorigin="anonymous"
      referrerpolicy="no-referrer"
    />
  </head>
  <body>
    <canvas id="network-bg"></canvas>

    <div class="container">
      <header>
        <h1><i class="fab fa-github"></i> GitHub Profile Finder</h1>
        <p>Search for any GitHub user to see their profile and repositories</p>
      </header>

      <div class="search-container">
        <input
          type="text"
          id="search"
          placeholder="Enter a GitHub username..."
          autocomplete="off"
        />
        <button id="search-btn">Search</button>
      </div>

      <div id="profile-container" class="hidden">
        <div class="profile-header">
          <img src="" alt="Profile avatar" id="avatar" />
          <div class="profile-info">
            <h2 id="name"></h2>
            <p id="username"></p>
            <p id="bio"></p>
            <div class="location-date">
              <p>
                <i class="fas fa-map-marker-alt"></i>
                <span id="location">Not specified</span>
              </p>
              <p>
                <i class="far fa-calendar-alt"></i>
                Joined
                <span id="joined-date"></span>
              </p>
            </div>
            <a id="profile-link" target="_blank" class="btn"> View Profile </a>
          </div>
        </div>

        <div class="stats">
          <div class="stat">
            <i class="fas fa-users"></i>
            <span id="followers"></span> followers
          </div>

          <div class="stat">
            <i class="fas fa-user-friends"></i>
            <span id="following"></span> following
          </div>
          <div class="stat">
            <i class="fas fa-code-branch"></i>
            <span id="repos"></span> repositories
          </div>
        </div>

        <div class="additional-info">
          <div class="info-item" id="company-container">
            <i class="fas fa-building"></i>
            <span id="company">Not specified</span>
          </div>
          <div class="info-item" id="blog-container">
            <i class="fas fa-link"></i>
            <a id="blog" target="_blank">No website</a>
          </div>
          <div class="info-item" id="twitter-container">
            <i class="fab fa-twitter"></i>
            <a id="twitter" target="_blank">No Twitter</a>
          </div>
        </div>

        <div class="repos-section">
          <h3>Latest Repositories</h3>
          <div id="repos-container" class="repos-container">
            <!-- Repositories will be inserted here dynamically -->

            <div class="loading-repos">Loading repositories...</div>
          </div>
        </div>
      </div>

      <div id="error-container" class="hidden">
        <p>No user found. Please try another username.</p>
      </div>
    </div>
    <script>
      const canvas = document.getElementById("network-bg");
      const ctx = canvas.getContext("2d");

      let width = (canvas.width = window.innerWidth);
      let height = (canvas.height = window.innerHeight);

      let points = [];
      const POINT_COUNT = 100;
      const MAX_DIST = 100;

      function resizeCanvas() {
        width = canvas.width = window.innerWidth;
        height = canvas.height = window.innerHeight;
      }

      window.addEventListener("resize", resizeCanvas);

      for (let i = 0; i < POINT_COUNT; i++) {
        points.push({
          x: Math.random() * width,
          y: Math.random() * height,
          vx: (Math.random() - 0.5) * 0.5,
          vy: (Math.random() - 0.5) * 0.5,
        });
      }

      function draw() {
        ctx.clearRect(0, 0, width, height);

        for (let i = 0; i < POINT_COUNT; i++) {
          let p1 = points[i];
          p1.x += p1.vx;
          p1.y += p1.vy;

          if (p1.x < 0 || p1.x > width) p1.vx *= -1;
          if (p1.y < 0 || p1.y > height) p1.vy *= -1;

          ctx.beginPath();
          ctx.arc(p1.x, p1.y, 2, 0, Math.PI * 2);
          ctx.fillStyle = "rgba(255,255,255,0.5)";
          ctx.fill();

          for (let j = i + 1; j < POINT_COUNT; j++) {
            let p2 = points[j];
            let dx = p1.x - p2.x;
            let dy = p1.y - p2.y;
            let dist = Math.sqrt(dx * dx + dy * dy);
            if (dist < MAX_DIST) {
              ctx.beginPath();
              ctx.moveTo(p1.x, p1.y);
              ctx.lineTo(p2.x, p2.y);
              ctx.strokeStyle = `rgba(255,255,255,${1 - dist / MAX_DIST})`;
              ctx.stroke();
            }
          }
        }

        requestAnimationFrame(draw);
      }

      draw();
    </script>

    <script src="script.js"></script>
  </body>
</html>
