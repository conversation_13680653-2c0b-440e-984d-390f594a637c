<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Glassmorphic To-Do App</title>
    <link rel="stylesheet" href="styles.css" />
  </head>
  <body>
    <div class="todo-container">
      <h2>To-Do App</h2>
      <div class="progress">
        <span id="progress-text">0 / 0</span>
        <div class="progress-indicator">
          <div id="progress-bar"></div>
        </div>
      </div>
      <div class="input-section">
        <input type="text" id="taskInput" placeholder="Add a new task..." />
        <input type="date" id="dueDate" />
        <button id="addTask">+</button>
      </div>
      <ul id="taskList"></ul>
    </div>

    <!-- Edit Task Modal -->
    <div class="modal" id="editModal">
      <div class="modal-content">
        <h3>Edit Task</h3>
        <input
          type="text"
          id="editTaskInput"
          placeholder="Edit task description"
        />
        <input type="date" id="editDueDate" />
        <div class="modal-buttons">
          <button id="saveEdit">Save</button>
          <button id="cancelEdit">Cancel</button>
        </div>
      </div>
    </div>

    <!-- Particles.js container -->
    <div id="particles-js"></div>

    <!-- Particles.js script -->
    <script src="https://cdn.jsdelivr.net/npm/particles.js@2.0.0/particles.min.js"></script>

    <script src="https://cdn.jsdelivr.net/npm/canvas-confetti@1.6.0/dist/confetti.browser.min.js"></script>
    <script src="script.js"></script>
  </body>
</html>
