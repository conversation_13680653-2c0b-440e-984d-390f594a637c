# 💸 Tip Calculator Web App

A responsive and stylish **Tip Calculator** built using HTML, CSS, and JavaScript. Easily calculate how much each person owes including tips, with live updates and a clean user interface.

---

## 🚀 Features

- 💵 Real-time tip calculation
- 👥 Split the bill between multiple people
- 📱 Fully responsive for mobile and desktop
- 🎨 Modern UI with vibrant colors and smooth layout
- 🔄 Reset button to clear all inputs and return to defaults
- 💰 Shows both per-person total and full bill total including tip
- ✅ Input validation to prevent incorrect values

---

## 📦 Project Structure

```
tip-calculator/
├── index.html          # Main HTML structure
├── styles.css          # Styling for layout, colors, and responsiveness
├── script.js           # JavaScript logic for calculations
└── README.md           # This file
```

---

## 🧑‍💻 How to Use

1. **Download or Clone this Repository**

```bash
git clone https://github.com/your-username/tip-calculator.git
cd tip-calculator
```

2. **Open `index.html` in your browser**

Just double-click the file, or use Live Server in VS Code for live editing.

---

## 🖼 Preview

<img src="preview.gif" width="100%" height="100%">

---

## 🔧 Tech Stack

- **HTML5**
- **CSS3**
- **Vanilla JavaScript**

---

## 📋 License

This project is open-source and free to use.

---

## 🙌 Author

Made with ❤️ by Pranava Sree Pottipati
🔗 [GitHub](https://github.com/pranavasree) | [LinkedIn](https://www.linkedin.com/in/pranava-sree-pottipati-422092172/)
