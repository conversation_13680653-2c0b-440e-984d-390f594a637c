<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Currency Converter App</title>
    <link rel="stylesheet" href="style.css" />
    <link
      href="https://fonts.googleapis.com/css?family=Poppins:100,100italic,200,200italic,300,300italic,regular,italic,500,500italic,600,600italic,700,700italic,800,800italic,900,900italic"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="app-layout override-columns">
      <!-- Left section: Currency rates -->
      <div class="side-section rates-card" id="rates-section">
        <h3>Currency Rates</h3>
        <table id="rates-table">
          <thead>
            <tr>
              <th>Currency</th>
              <th>Rate</th>
              <th>Converted</th>
            </tr>
          </thead>
          <tbody id="rates-body"></tbody>
        </table>
      </div>

      <!-- Middle section: Main converter -->
      <div class="main-section">
        <div class="converter-card">
          <div class="header">
            <h1>Currency Converter</h1>
            <button id="theme-toggle" class="theme-toggle">🌓</button>
          </div>

          <form id="converter-form">
            <div class="form-group">
              <label for="amount">Amount</label>
              <input
                type="number"
                id="amount"
                placeholder="Enter amount"
                required
              />
            </div>

            <div class="currency-row">
              <div class="form-group from-currency">
                <label for="from-currency">From</label>
                <select id="from-currency">
                  <!-- Currency options will be inserted from our api -->
                </select>
              </div>

              <button type="button" id="swap-btn" class="swap-btn">↔️</button>

              <div class="form-group to-currency">
                <label for="to-currency">To</label>
                <select id="to-currency">
                  <!-- Currency options will be inserted from our api -->
                </select>
              </div>
            </div>

            <button type="submit" class="convert-btn">Convert</button>
          </form>

          <div id="result">
            <div id="loading" class="loading-spinner hidden"></div>
            <div id="result-text"></div>
          </div>

          <div class="quick-convert">
            <h3>Quick Convert</h3>
            <div class="currency-pairs" id="currency-pairs">
              <!-- Will be populated with common pairs -->
            </div>
          </div>
        </div>
      </div>

      <!-- Right section: Conversion history -->
      <div class="side-section history-card" id="history-section">
        <h3>Conversion History</h3>
        <ul id="history-list" class="history-list"></ul>
      </div>
    </div>

    <script src="script.js"></script>
  </body>
</html>
