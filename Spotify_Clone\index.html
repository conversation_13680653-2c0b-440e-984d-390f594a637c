<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta
      name="description"
      content="Spotify Clone - Your favorite music streaming experience"
    />
    <title>Spotify - Your favourite music is here</title>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
    />
    <link rel="stylesheet" href="style.css" />
  </head>
  <body>
    <!-- Sidebar -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <div class="brand">
          <img src="logo.png" alt="Spotify Logo" />
          <span>Spotify</span>
        </div>
      </div>

      <nav class="sidebar-nav">
        <ul>
          <li class="nav-item active">
            <i class="fas fa-home"></i>
            <span>Home</span>
          </li>
          <li class="nav-item">
            <i class="fas fa-search"></i>
            <span>Search</span>
          </li>
          <li class="nav-item">
            <i class="fas fa-list"></i>
            <span>Your Library</span>
          </li>
        </ul>

        <div class="playlist-section">
          <ul>
            <li class="nav-item">
              <i class="fas fa-plus-square"></i>
              <span>Create Playlist</span>
            </li>
            <li class="nav-item">
              <i class="fas fa-heart"></i>
              <span>Liked Songs</span>
            </li>
          </ul>
        </div>
      </nav>
    </aside>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Top Bar -->
      <header class="top-bar">
        <div class="nav-buttons">
          <button class="nav-btn" id="backBtn">
            <i class="fas fa-chevron-left"></i>
          </button>
          <button class="nav-btn" id="forwardBtn">
            <i class="fas fa-chevron-right"></i>
          </button>
        </div>

        <div class="search-container">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input
              type="text"
              id="searchInput"
              placeholder="Search for songs, artists, or albums"
            />
            <button class="clear-search" id="clearSearch">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>

        <div class="user-controls">
          <button class="control-btn" id="shuffleBtn" title="Shuffle">
            <i class="fas fa-random"></i>
          </button>
          <button class="control-btn" id="repeatBtn" title="Repeat">
            <i class="fas fa-redo"></i>
          </button>
          <div class="volume-control">
            <i class="fas fa-volume-up"></i>
            <input
              type="range"
              id="volumeSlider"
              min="0"
              max="100"
              value="50"
            />
          </div>
        </div>
      </header>

      <!-- Content Area -->
      <div class="content-area">
        <div class="playlist-header">
          <div class="playlist-cover">
            <img src="bg.jpg" alt="Playlist Cover" />
            <div class="play-overlay">
              <button class="play-all-btn" id="playAllBtn">
                <i class="fas fa-play"></i>
              </button>
            </div>
          </div>
          <div class="playlist-info">
            <span class="playlist-type">PLAYLIST</span>
            <h1 class="playlist-title">Best of NCS - No Copyright Sounds</h1>
            <p class="playlist-description">
              The best electronic music from NCS
            </p>
            <div class="playlist-stats">
              <span class="song-count">10 songs</span>
              <span class="duration">• 45 min</span>
            </div>
          </div>
        </div>

        <!-- Song List -->
        <div class="song-list-container">
          <div class="song-list-header">
            <div class="header-item number">#</div>
            <div class="header-item title">TITLE</div>
            <div class="header-item album">ALBUM</div>
            <div class="header-item duration">
              <i class="far fa-clock"></i>
            </div>
          </div>

          <div class="song-list" id="songList">
            <!-- Songs will be dynamically loaded here -->
          </div>
        </div>
      </div>
    </main>
    <!-- Music Player -->
    <footer class="music-player">
      <div class="player-left">
        <div class="current-song">
          <img src="covers/1.jpg" alt="Current Song" id="currentSongImage" />
          <div class="song-details">
            <div class="song-title" id="currentSongTitle">
              Warriyo - Mortals
            </div>
            <div class="song-artist" id="currentSongArtist">NCS Release</div>
          </div>
          <button class="like-btn" id="likeBtn">
            <i class="far fa-heart"></i>
          </button>
        </div>
      </div>

      <div class="player-center">
        <div class="player-controls">
          <button class="control-btn" id="shufflePlayerBtn" title="Shuffle">
            <i class="fas fa-random"></i>
          </button>
          <button class="control-btn" id="previousBtn">
            <i class="fas fa-step-backward"></i>
          </button>
          <button class="play-btn" id="masterPlayBtn">
            <i class="fas fa-play"></i>
          </button>
          <button class="control-btn" id="nextBtn">
            <i class="fas fa-step-forward"></i>
          </button>
          <button class="control-btn" id="repeatPlayerBtn" title="Repeat">
            <i class="fas fa-redo"></i>
          </button>
        </div>

        <div class="progress-container">
          <span class="time-current" id="currentTime">0:00</span>
          <div class="progress-bar-container">
            <input
              type="range"
              class="progress-bar"
              id="progressBar"
              min="0"
              max="100"
              value="0"
            />
            <div class="progress-fill" id="progressFill"></div>
          </div>
          <span class="time-total" id="totalTime">0:00</span>
        </div>
      </div>

      <div class="player-right">
        <button class="control-btn" id="queueBtn" title="Queue">
          <i class="fas fa-list"></i>
        </button>
        <button class="control-btn" id="deviceBtn" title="Connect to device">
          <i class="fas fa-desktop"></i>
        </button>
        <div class="volume-container">
          <button class="control-btn" id="volumeBtn">
            <i class="fas fa-volume-up"></i>
          </button>
          <input
            type="range"
            class="volume-bar"
            id="volumeBar"
            min="0"
            max="100"
            value="50"
          />
        </div>
        <button class="control-btn" id="fullscreenBtn" title="Fullscreen">
          <i class="fas fa-expand"></i>
        </button>
      </div>
    </footer>

    <!-- Audio Element -->
    <audio id="audioElement" preload="metadata"></audio>

    <script src="script.js"></script>
  </body>
</html>
