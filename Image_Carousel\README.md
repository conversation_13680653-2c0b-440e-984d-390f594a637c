# 🎠 Responsive Profile Card Carousel

This is a responsive profile card carousel built with [Swiper.js](https://swiperjs.com/). It displays user profile cards in a smooth, animated slider with navigation arrows and pagination dots. The layout adjusts gracefully across all screen sizes.

---

## ✨ Features

- Responsive card slider with 1–3 cards per view
- Smooth swipe animations and transitions
- Navigation arrows and clickable pagination dots
- Glassmorphic styled profile cards
- Mobile-friendly layout and design

---

## 📸 Preview

![carousel preview](preview.gif)

---

## 🚀 Technologies Used

- HTML5
- CSS3
- JavaScript (ES6)
- [Swiper.js](https://swiperjs.com/) (Carousel library)

---

## 📁 Project Structure

```
├── index.html          # Main HTML page
├── style.css           # Styling for the carousel and cards
├── script.js           # JavaScript for Swiper configuration
└── README.md           # Project description and setup
```

---

## 📦 Setup Instructions

1. **Clone or download this repository**

   ```bash
   git clone https://github.com/your-username/carousel-app.git
   cd carousel-app
   ```

2. **Open the `index.html` file**
   You can use VS Code's Live Server or simply double-click the file to open in your browser.

3. **Enjoy the interactive carousel!**

---

## 🔧 Customization

- To add or modify profile cards, edit the `.card` elements in `index.html`.
- Update images using free APIs like [randomuser.me](https://randomuser.me/photos).
- Adjust `slidesPerView` values in `script.js` to control how many cards appear at each screen size.

---

## 📱 Responsive Behavior

| Screen Width     | Cards Displayed |
| ---------------- | --------------- |
| `< 600px`        | 1 card          |
| `600px - 1023px` | 2 cards         |
| `>= 1024px`      | 3 cards         |

---

## 📜 License

This project is open-source and free to use.

---

## 🙌 Acknowledgements

- Thanks to [Swiper.js](https://swiperjs.com/) for the amazing carousel engine.
- Profile images provided by [randomuser.me](https://randomuser.me/).

---

## 💡 Author

Created by **Pranava Sree Pottipati** — feel free to connect!
