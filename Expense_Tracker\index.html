<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Expense Tracker</title>
    <link rel="stylesheet" href="style.css" />
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  </head>
  <body>
    <div class="container">
      <!-- Header Section -->
      <div class="header">
        <h1>Expense Tracker</h1>
        <div class="toggle-container">
          <span class="toggle-label">Dark Mode</span>
          <input type="checkbox" id="dark-mode-toggle" class="toggle" />
        </div>
      </div>

      <!-- Balance Section -->
      <div class="balance-container">
        <h2>Your Balance</h2>
        <h1 id="balance">$0.00</h1>
        <div class="budget-group">
          <label for="budget">Monthly Budget</label>
          <input
            type="number"
            id="budget"
            min="0"
            step="0.01"
            placeholder="Enter budget"
          />
          <div class="progress-bar">
            <div class="progress-bar-fill" id="budget-progress"></div>
          </div>
        </div>
      </div>

      <!-- Summary Section -->
      <div class="summary">
        <div class="income">
          <h3>Income</h3>
          <p id="income-amount">$0.00</p>
        </div>
        <div class="expenses">
          <h3>Expenses</h3>
          <p id="expense-amount">$0.00</p>
        </div>
        <div class="chart-container">
          <h3>Category Breakdown</h3>
          <canvas id="category-chart"></canvas>
        </div>
      </div>

      <!-- Main Content Section -->
      <div class="main-content">
        <!-- Transaction List -->
        <div class="transaction-container">
          <h2>Transactions</h2>
          <div class="filter-container">
            <input
              type="text"
              id="search"
              placeholder="Search transactions..."
            />
            <select id="filter-type">
              <option value="all">All</option>
              <option value="income">Income</option>
              <option value="expense">Expense</option>
            </select>
            <select id="sort-by">
              <option value="date-desc">Date (Newest)</option>
              <option value="date-asc">Date (Oldest)</option>
              <option value="amount-desc">Amount (High to Low)</option>
              <option value="amount-asc">Amount (Low to High)</option>
            </select>
            <button id="export-btn">Export CSV</button>
          </div>
          <ul id="transaction-list"></ul>
        </div>

        <!-- Transaction Form -->
        <div class="form-container">
          <h2>Add Transaction</h2>
          <form id="transaction-form">
            <div class="form-group">
              <label for="description">Description</label>
              <input type="text" id="description" name="description" required />
            </div>
            <div class="form-group">
              <label for="amount">Amount</label>
              <input
                type="number"
                id="amount"
                name="amount"
                step="0.01"
                required
              />
              <small>Use negative (-) for expenses</small>
            </div>
            <div class="form-group">
              <label for="category">Category</label>
              <select id="category" name="category" required>
                <option value="Food">Food</option>
                <option value="Rent">Rent</option>
                <option value="Salary">Salary</option>
                <option value="Transport">Transport</option>
                <option value="Entertainment">Entertainment</option>
                <option value="Other">Other</option>
              </select>
            </div>
            <div class="form-group">
              <label for="date">Date</label>
              <input type="date" id="date" name="date" required />
            </div>
            <button type="submit">Add Transaction</button>
          </form>
        </div>
      </div>
    </div>

    <script src="script.js"></script>
  </body>
</html>
