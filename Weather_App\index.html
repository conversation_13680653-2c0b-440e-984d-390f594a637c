<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Weather App</title>
    
    <link rel="stylesheet" href="styles.css"></link>
    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200&icon_names=humidity_percentage" /> -->
    <!-- <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@20..48,100..700,0..1,-50..200&icon_names=location_on" /> -->
    <!-- Importing Google Icons -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined&display=swap">

      
  </head>
  <body>
    <div class="card">
      <div class="search">
        <input type="text" placeholder="Please Enter City Name" spellcheck="false">
       <button><img src="images/search.png"/></button> 
      </div>
      <div class="weather-info" style="display: none;">
        <div class="loaction-date-container">
          <div class="location"><span class="material-symbols-outlined">
            location_on
            </span>
            <h4 class="country-text">Jakarta</h4>
          </div>
           <h5 class="current-date-text regular-text">Web, 07 Aug</h5>
        </div>
        <div class="weather-summary">
          <img src="images/weather/clouds.svg" class="weather-summary-img">
         <div class="weather-summary-info">
          <h1 class="temp-text">20 °C</h1>
          <h3 class="condition-text regular-text">Clouds</h3>
        </div>
        </div>
        <div class="weather-conditions">
          <div class="condition-item">
            <span class="material-symbols-outlined">
              water_drop
              </span>
              <div class="condition-info">
                <h5 class="regular-text">Humidity</h5>
                <h5 class="humidity-value-text">55%</h5>
              </div>
          </div>
          <div class="condition-item">
            <span class="material-symbols-outlined">
              air
              </span>
              <div class="condition-info">
                <h5 class="regular-text">Wind Speed</h5>
                <h5 class="wind-value-text">2 M/s</h5>
              </div>
          </div>
        </div>
        <div class="forecast-items">
          <div class="forecast-item">
            <h5 class="forecast-item-date regular-text">05 Aug</h5>
            <img src="images/weather/thunderstorm.svg" class="forecast-item-img">
            <h5 class="forecast-item-temp">20 °C</h5>
          </div>
          <div class="forecast-item">
            <h5 class="forecast-item-date regular-text">05 Aug</h5>
            <img src="images/weather/thunderstorm.svg" class="forecast-item-img">
            <h5 class="forecast-item-temp">20 °C</h5>
          </div>
          <div class="forecast-item">
            <h5 class="forecast-item-date regular-text">05 Aug</h5>
            <img src="images/weather/thunderstorm.svg" class="forecast-item-img">
            <h5 class="forecast-item-temp">20 °C</h5>
          </div>
          <div class="forecast-item">
            <h5 class="forecast-item-date regular-text">05 Aug</h5>
            <img src="images/weather/thunderstorm.svg" class="forecast-item-img">
            <h5 class="forecast-item-temp">20 °C</h5>
          </div>
        </div>
      </div>  
      <div class="search-city section-message">
        <img src="images/message/search-city.png" class="message-img">
        <h1>Search City</h1>
        <h4 class="regular-tex">Find out the weather conditions of the city</h4>
      </div>  
      
      <div class="not-found section-message" style="display: none;">
        <img src="images/message/not-found.png" class="message-img">
        <h1>Search City</h1>
        <h4 class="regular-tex">Find out the weather conditions of the city</h4>
      </div> 
    </div>
    <script src="script.js" defer></script> 
    <!-- The defer attribute in <script> is used to ensure the JavaScript file loads without blocking the HTML rendering and executes only after the full HTML document is parsed. -->
  </body>
</html>
