# 🌈 Glassmorphic Responsive Accordion App

This project is a beautifully styled **responsive accordion component** featuring a **glassmorphism UI**, smooth animations, and support for **nested accordions**. It is built using pure **HTML**, **CSS**, and **JavaScript**.

## ✨ Features

- Glassmorphism-inspired UI (blurred background, semi-transparent panels)
- Responsive design for desktop, tablet, and mobile
- Accordion-style toggle with smooth animation
- Icon rotation on toggle
- Nested accordion support
- Clean and modern UI with color themes for each section

---

## 📸 Preview

<image src="preview.gif" alt="Accordion Preview" width="100%" height="100%">
_Elegant glass-effect with color-coded sections._

---

## 🛠️ Technologies Used

- HTML5
- CSS3 (with media queries)
- Vanilla JavaScript (for toggle logic)

---

## 📂 Project Structure

```bash
📁 accordion-app/
├── index.html          # Main HTML structure
├── styles.css          # Glassmorphism styling and responsive layout
├── script.js           # Toggle logic for accordions
└── README.md           # This file
```

---

## 🚀 How to Run

1. Clone or download the repository:

   ```bash
   git clone https://github.com/your-username/glassmorphic-accordion.git
   ```

2. Open the project folder:

   ```bash
   cd glassmorphic-accordion
   ```

3. Open `index.html` in your browser:
   - You can simply double-click it
   - Or run it with a local server if needed

---

## 📱 Responsive Design

This app is fully responsive:

- ✅ Desktop
- ✅ Tablet
- ✅ Mobile

All layouts are automatically optimized based on screen width.

---

## 📃 License

This project is open-source and free to use for personal or commercial projects.

---

## 🙌 Acknowledgements

Design inspired by modern glassmorphism UI trends and minimalist UX patterns.
