# Glassmorphic Stopwatch App ⏱️

A visually appealing stopwatch built with HTML, CSS, and JavaScript using a glassmorphism design. This app includes stopwatch, lap, and reset functionality, with animated shapes in the background for aesthetic enhancement.

## 🌟 Features

- Elegant glassmorphic UI
- Animated background elements (top & center)
- Responsive design for mobile and desktop
- Stopwatch with Start, Stop, Reset, and Lap buttons
- Lap history with scrollable list

## 📁 Files Included

- `index.html` – Main HTML structure
- `style.css` – Styling with glassmorphism and animations
- `script.js` – Functionality for the stopwatch
- `README.md` – This file

## 🚀 How to Use

1. Download or clone the repository.
2. Open `index.html` in any modern web browser.
3. Use the controls to start, stop, reset, or record laps.

## 📷 Preview

![Preview Screenshot](preview.gif) <!-- Optional: Add your own screenshot -->

## 📦 Deployment

No build tools are required. Just open the `index.html` file directly in your browser.

## 🛠 Technologies Used

- HTML5
- CSS3 (Glassmorphism & Animations)
- JavaScript (Vanilla)

## 📄 License

This project is open source and free to use.
