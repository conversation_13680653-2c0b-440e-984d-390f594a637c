body {
  font-family: "Poppins", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>,
    sans-serif;
  margin: 0;
  padding: 32px;
  background-color: #26313b;
  color: #333;
  line-height: 1.6;
  display: flex;
  justify-content: center;
  min-height: 100vh;
}

.container {
  display: flex;
  gap: 24px;
  max-width: 1280px;
  width: 100%;
  margin: 80px auto;
  border-radius: 12px;
  align-items: stretch;
}

.editor,
.preview {
  flex: 1;
  background: #ffffff;
  padding: 24px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
  transition: box-shadow 0.3s ease, border-color 0.2s ease;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  height: 580px; /* Fixed height for both containers */
}

.editor:hover,
.preview:hover {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.editor h2,
.preview h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 16px;
  text-align: left;
}

.editor textarea {
  width: 100%;
  flex: 1; /* Takes remaining space to match preview content area */
  min-height: 0; /* Allows flex to shrink if needed */
  resize: none;
  border: none;
  border-radius: 8px;
  padding: 12px;
  font-family: "Fira Code", "Courier New", monospace;
  font-size: 1rem;
  background-color: #f8fafc;
  transition: box-shadow 0.2s ease;
  box-sizing: border-box;
  line-height: 1.5;
}

.editor textarea:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.preview {
  overflow-y: auto;
  background-color: #f8fafc;
  flex: 1; /* Ensures content area takes remaining space */
  min-height: 0; /* Allows flex to shrink if needed */
  padding: 12px; /* Added padding to match textarea and prevent hidden text */
}

.preview > div {
  /* Target the inner div generated by marked.parse() */
  width: 100%;
  padding-left: 12px; /* Additional padding to ensure left alignment */
  box-sizing: border-box;
}

.preview * {
  max-width: 100%;
  word-wrap: break-word;
  font-family: "Fira Code", "Courier New", monospace;
  font-size: 1rem;
  line-height: 1.5;
  color: #333;
  margin: 0; /* Reset margins to avoid overlap */
}

.preview p,
.preview ul,
.preview ol,
.preview blockquote {
  margin: 0 0 12px;
  padding-left: 10px;
}

.toolbar {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
  flex-wrap: wrap;
  align-items: center;
  justify-content: flex-start;
}

.toolbar button,
.toolbar input {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background-color: #253245;
  color: white;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease, transform 0.1s ease;
  font-size: 0.9rem;
  min-width: 80px;
  text-align: center;
}

.toolbar button:hover,
.toolbar input:hover {
  background-color: #2563eb;
  transform: translateY(-1px);
}

.toolbar button:active,
.toolbar input:active {
  transform: translateY(0);
}

.toolbar input[type="file"] {
  padding: 8px 12px;
  background-color: #6b7280;
  min-width: auto;
}

.toolbar input[type="file"]::-webkit-file-upload-button {
  background: transparent;
  border: none;
  color: white;
  cursor: pointer;
  font-weight: 500;
  padding: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  margin-top: 0;
  color: #1a202c;
  font-weight: 600;
  font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
    sans-serif;
}

pre,
code {
  border-radius: 6px;
  background-color: #1a202c !important;
  color: #e2e8f0 !important;
  padding: 4px 6px;
  font-family: "Fira Code", "Courier New", monospace;
}

@media (max-width: 768px) {
  body {
    padding: 16px;
  }
  .container {
    flex-direction: column;
    gap: 16px;
  }
  .editor,
  .preview {
    padding: 16px;
    height: 500px; /* Adjusted for mobile */
  }
  .editor textarea,
  .preview {
    flex: 1;
    min-height: 0;
  }
  .toolbar {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
  }
  .toolbar button,
  .toolbar input {
    flex: 1 1 calc(50% - 4px);
    min-width: 0;
    text-align: center;
  }
}
