# Glassmorphic To-Do App

A beautifully styled to-do list app with glassmorphism design, confetti celebration, overdue alerts, and localStorage persistence.

## ✨ Features

- ✅ Add, edit, delete tasks
- 📅 Assign due dates to tasks
- ⚠️ Overdue task alert
- 📊 Progress bar showing completed/total tasks
- 🎉 Confetti and sound celebration when all tasks are completed
- 🧊 Stunning glassmorphic UI design
- 💾 Tasks saved to browser localStorage
- 🌌 Animated particle background

## 📁 File Structure

```
/project-folder
│
├── index.html         # Main HTML file
├── styles.css         # CSS styles with glassmorphism
├── script.js          # JavaScript logic
├── images/
│   └── celebration.mp3 # Confetti celebration sound
└── README.md          # This file
```

## 🚀 Getting Started

1. **Clone or download the repository.**
2. **Open `index.html` in your browser.**
3. **Start adding tasks!**

## 🔧 Dependencies

- [Particles.js](https://vincentgarreau.com/particles.js/) for background particles.
- [Canvas Confetti](https://www.npmjs.com/package/canvas-confetti) for confetti celebration.

CDNs are used, so no installation is required.

## 📸 Preview

<image src="preview.gif" alt="To-Do App Preview" width="100%" height="100%">

## 📦 Deployment

You can deploy this app on:

- GitHub Pages
- Netlify
- Vercel

## 📜 License

This project is open-source and free to use.
