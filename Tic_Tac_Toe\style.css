@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");

/* CSS Variables for Theme */
:root {
  --primary-color: #707175;
  --secondary-color: #322e36;
  --accent-color: #856489;
  --success-color: #4ecdc4;
  --warning-color: #ffe066;
  --danger-color: #ff6b6b;

  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-tertiary: #e9ecef;
  --text-primary: #2c3e50;
  --text-secondary: #6c757d;
  --text-light: #adb5bd;

  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);

  --border-radius: 12px;
  --border-radius-large: 20px;
  --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Dark Theme Variables */
[data-theme="dark"] {
  --bg-primary: #1a1a2e;
  --bg-secondary: #16213e;
  --bg-tertiary: #0f3460;
  --text-primary: #eee;
  --text-secondary: #cbd5e0;
  --text-light: #a0aec0;

  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.4);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.5);
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Poppins", sans-serif;
  background: linear-gradient(
    135deg,
    var(--primary-color),
    var(--secondary-color)
  );
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  transition: var(--transition);
}

/* Game Container */
.game-container {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-heavy);
  padding: 30px;
  max-width: 600px;
  width: 100%;
  position: relative;
  overflow: hidden;
}

.game-container::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(
    90deg,
    var(--primary-color),
    var(--accent-color),
    var(--secondary-color)
  );
}

/* Header */
.game-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--bg-tertiary);
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo i {
  font-size: 28px;
  color: var(--primary-color);
}

.logo h1 {
  color: var(--text-primary);
  font-size: 28px;
  font-weight: 700;
  background: linear-gradient(
    45deg,
    var(--primary-color),
    var(--secondary-color)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.theme-btn {
  background: var(--bg-secondary);
  border: none;
  border-radius: 50%;
  width: 45px;
  height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  color: var(--text-secondary);
  box-shadow: var(--shadow-light);
}

.theme-btn:hover {
  background: var(--primary-color);
  color: white;
  transform: scale(1.1);
}

/* Game Mode Selection */
.game-mode-selection {
  text-align: center;
  animation: fadeInUp 0.6s ease;
}

.game-mode-selection h2 {
  color: var(--text-primary);
  margin-bottom: 30px;
  font-size: 24px;
  font-weight: 600;
}

.mode-buttons {
  display: grid;
  gap: 20px;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
}

.mode-btn {
  background: var(--bg-secondary);
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  padding: 25px 20px;
  cursor: pointer;
  transition: var(--transition);
  text-align: center;
  box-shadow: var(--shadow-light);
  position: relative;
  overflow: hidden;
}

.mode-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.mode-btn:hover::before {
  left: 100%;
}

.mode-btn:hover {
  border-color: var(--primary-color);
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

.mode-btn i {
  font-size: 32px;
  color: var(--primary-color);
  margin-bottom: 12px;
  display: block;
}

.mode-btn span {
  display: block;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 8px;
}

.mode-btn small {
  color: var(--text-secondary);
  font-size: 12px;
}

/* Game Section */
.game-section {
  animation: fadeInUp 0.6s ease;
}

.game-info {
  margin-bottom: 30px;
}

.player-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.player {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  transition: var(--transition);
  min-width: 140px;
}

.player.active {
  background: linear-gradient(
    45deg,
    var(--primary-color),
    var(--secondary-color)
  );
  color: white;
  transform: scale(1.05);
  box-shadow: var(--shadow-medium);
}

.player-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  font-size: 18px;
}

.player-x .player-icon {
  color: var(--danger-color);
}

.player-o .player-icon {
  color: var(--success-color);
}

.player.active .player-icon {
  color: white;
  background: rgba(255, 255, 255, 0.3);
}

.player-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.player-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--text-primary);
}

.player.active .player-name {
  color: white;
}

.player-score {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
}

.player.active .player-score {
  color: white;
}

.game-status {
  text-align: center;
  flex: 1;
}

.current-turn {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.game-mode-display {
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Game Board */
.game-board-container {
  position: relative;
  margin-bottom: 30px;
  display: flex;
  justify-content: center;
}

.game-board {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  background: var(--bg-tertiary);
  padding: 8px;
  border-radius: var(--border-radius);
  box-shadow: var(--shadow-medium);
  max-width: 300px;
  width: 100%;
}

.cell {
  aspect-ratio: 1;
  background: var(--bg-primary);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition);
  font-size: 36px;
  font-weight: 700;
  position: relative;
  overflow: hidden;
}

.cell::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    45deg,
    var(--primary-color),
    var(--secondary-color)
  );
  opacity: 0;
  transition: var(--transition);
}

.cell:hover::before {
  opacity: 0.1;
}

.cell:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-light);
}

.cell.disabled {
  cursor: not-allowed;
}

.cell.disabled:hover {
  transform: none;
  box-shadow: none;
}

.cell.disabled:hover::before {
  opacity: 0;
}

.cell .symbol {
  position: relative;
  z-index: 1;
  animation: symbolAppear 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.cell .symbol.x {
  color: var(--danger-color);
}

.cell .symbol.o {
  color: var(--success-color);
}

/* Winning Line */
.winning-line {
  position: absolute;
  background: linear-gradient(45deg, var(--accent-color), var(--warning-color));
  border-radius: 4px;
  opacity: 0;
  transition: var(--transition);
  z-index: 10;
}

.winning-line.show {
  opacity: 1;
  animation: drawLine 0.5s ease;
}

/* Game Controls */
.game-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.control-btn {
  background: var(--bg-secondary);
  border: 2px solid transparent;
  border-radius: var(--border-radius);
  padding: 12px 20px;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
  font-family: inherit;
  font-weight: 500;
  color: var(--text-primary);
  box-shadow: var(--shadow-light);
}

.control-btn:hover {
  border-color: var(--primary-color);
  background: var(--primary-color);
  color: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.control-btn i {
  font-size: 16px;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes symbolAppear {
  from {
    opacity: 0;
    transform: scale(0) rotate(180deg);
  }
  to {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes drawLine {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: var(--transition);
  backdrop-filter: blur(5px);
}

.modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-heavy);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  transform: scale(0.7);
  transition: var(--transition);
}

.modal.show .modal-content {
  transform: scale(1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  border-bottom: 2px solid var(--bg-tertiary);
}

.modal-header h3 {
  color: var(--text-primary);
  font-size: 20px;
  font-weight: 600;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: var(--transition);
}

.close-btn:hover {
  background: var(--danger-color);
  color: white;
}

.modal-body {
  padding: 25px;
}

/* Statistics Modal */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 25px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: var(--bg-secondary);
  border-radius: var(--border-radius);
  transition: var(--transition);
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-light);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: 8px;
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.reset-stats-btn {
  width: 100%;
  background: var(--danger-color);
  border: none;
  border-radius: var(--border-radius);
  padding: 12px;
  color: white;
  font-family: inherit;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.reset-stats-btn:hover {
  background: #e55555;
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

/* Result Modal */
.result-modal {
  text-align: center;
}

.result-content {
  padding: 40px 25px;
}

.result-icon {
  font-size: 64px;
  margin-bottom: 20px;
  animation: bounceIn 0.6s ease;
}

.result-icon.win {
  color: var(--success-color);
}

.result-icon.draw {
  color: var(--warning-color);
}

.result-text {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 30px;
}

.result-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.result-btn {
  background: var(--primary-color);
  border: none;
  border-radius: var(--border-radius);
  padding: 12px 20px;
  color: white;
  font-family: inherit;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-btn:hover {
  background: var(--secondary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
}

.change-mode-btn {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.change-mode-btn:hover {
  background: var(--text-secondary);
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  body {
    padding: 10px;
  }

  .game-container {
    padding: 20px;
  }

  .logo h1 {
    font-size: 24px;
  }

  .mode-buttons {
    grid-template-columns: 1fr;
  }

  .player-info {
    flex-direction: column;
    gap: 15px;
  }

  .player {
    justify-content: center;
    min-width: auto;
    width: 100%;
  }

  .game-status {
    order: -1;
    margin-bottom: 15px;
  }

  .game-board {
    max-width: 280px;
  }

  .cell {
    font-size: 28px;
  }

  .game-controls {
    flex-direction: column;
    align-items: center;
  }

  .control-btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .result-actions {
    flex-direction: column;
    align-items: center;
  }

  .result-btn {
    width: 100%;
    max-width: 200px;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .game-container {
    padding: 15px;
  }

  .logo h1 {
    font-size: 20px;
  }

  .logo i {
    font-size: 24px;
  }

  .theme-btn {
    width: 40px;
    height: 40px;
  }

  .mode-btn {
    padding: 20px 15px;
  }

  .mode-btn i {
    font-size: 28px;
  }

  .game-board {
    max-width: 250px;
  }

  .cell {
    font-size: 24px;
  }

  .current-turn {
    font-size: 16px;
  }

  .modal-content {
    width: 95%;
  }

  .modal-header,
  .modal-body {
    padding: 20px;
  }

  .result-content {
    padding: 30px 20px;
  }

  .result-icon {
    font-size: 48px;
  }

  .result-text {
    font-size: 20px;
  }
}

/* Additional Animations */
@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.pulse {
  animation: pulse 1s infinite;
}

/* Loading Animation */
.loading {
  position: relative;
  overflow: hidden;
}

.loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}
