# 🎬 FilmFinder – Discover Your Perfect Movie

**FilmFinder** is a powerful and beautifully designed movie discovery web app built with HTML, CSS, and vanilla JavaScript. Powered by the TMDb API, it allows users to search for, filter, and explore movies across genres, languages, and decades—all within a fully responsive, modern interface.

## ![Screenshot](preview.gif)

## ✨ Features

### 🔍 Movie Search & Filtering

- Real-time movie search with exact title match
- Filter by **release year** and **original language**

### 🌟 Dynamic Results

- Movie cards with title, year, rating, poster, and language
- Star ratings and animated hover effects

### 🎞️ Movie Details Modal

- Click any movie to view full details:
  - Overview, cast, director, runtime, language
  - YouTube trailer embed (if available)
  - Budget and revenue info

### 🎯 Popular Categories & Genres

- Navigate by **Popular Now**, **Top Rated**, **Upcoming**
- Explore movies by genre: Action, Comedy, Drama, Sci-Fi, and more

### 📑 Watchlist with LocalStorage

- Bookmark movies to a persistent **watchlist**
- Remove or add to watchlist with animations and visual indicators

### 🌓 Dark Mode Toggle

- Smooth dark/light mode switching with stored user preference

### 🔄 Pagination

- Navigate results across pages with live updates

### 📦 Toast Notifications

- Dynamic toast alerts for watchlist actions and feedback

### 📱 Fully Responsive Design

- Optimized for desktop, tablet, and mobile devices

---

## 🛠️ Tech Stack

- **HTML5**, **CSS3** (Flexbox, Grid, Responsive)
- **JavaScript (ES6+)**
- **TMDb API**
- **LocalStorage** for state persistence

---

## 🚀 Getting Started

1. Clone the repository:

```bash
git clone https://github.com/yourusername/filmfinder-app.git
cd filmfinder-app
```

2. Add your TMDb API key inside `app.js`:

```js
const API_KEY = "your_tmdb_api_key_here";
```

3. Open `index.html` in your browser.

---

## 🧑‍💻 Author

**Pranava Sree Pottipati**  
Front-End Developer passionate about building delightful and accessible web experiences.

---

## 📄 License

This project is licensed under the [MIT License](LICENSE).

---

> ⚠️ This app uses the TMDb API but is not endorsed or certified by TMDb.
