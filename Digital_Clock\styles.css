@import url("https://fonts.googleapis.com/css2?family=Montez&family=Montserrat:ital,wght@0,100..900;1,100..900&family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto+Condensed&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Poppins", sans-serif;
}

body {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  min-height: 100vh;
  background: #1e1e1e;
  padding: 20px;
}

.toggle-container {
  display: flex;
  align-items: center;
  gap: 10px;
  color: #fff;
  font-size: 1rem;
  margin-top: -80px;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  background-color: #ccc;
  border-radius: 24px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 20px;
  width: 20px;
  left: 2px;
  bottom: 2px;
  background-color: #fff;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #4caf50;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.time {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 40px;
  color: #fff;
  flex-wrap: wrap;
  margin-top: 80px;
}

.time .circle {
  width: 150px;
  height: 150px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.time .circle svg {
  position: relative;
  width: 150px;
  height: 150px;
  display: flex;
  justify-content: center;
  align-items: center;
  transform: rotate(270deg);
}

.time .circle svg circle {
  width: 100%;
  height: 100%;
  fill: transparent;
  stroke: #191919;
  stroke-width: 4px;
  transform: translate(5px, 5px);
}

.time .circle svg circle:nth-child(2) {
  stroke: var(--clr);
  stroke-dasharray: 440;
}

.time div {
  position: absolute;
  text-align: center;
  font-weight: 500;
  font-size: 1.5rem;
}

.time div span {
  position: absolute;
  transform: translateX(-8px);
  font-size: 0.35em;
  font-weight: 300;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.time .meridiem {
  position: relative;
  font-size: 1rem;
  transform: translateY(-100px);
}

.dots {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
}

.dots::before {
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  background: var(--clr);
  border-radius: 50%;
  top: -3px;
  box-shadow: 0 0 20px var(--clr), 0 0 60px var(--clr);
}

/* Responsive Design */
@media (max-width: 768px) {
  .time {
    gap: 20px;
  }

  .time .circle {
    width: 120px;
    height: 120px;
  }

  .time .circle svg {
    width: 120px;
    height: 120px;
  }

  .time div {
    font-size: 1.2rem;
  }

  .toggle-container {
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .time .circle {
    width: 100px;
    height: 100px;
  }

  .time .circle svg {
    width: 100px;
    height: 100px;
  }

  .time div {
    font-size: 1rem;
  }

  .toggle-container {
    font-size: 0.85rem;
  }
}
