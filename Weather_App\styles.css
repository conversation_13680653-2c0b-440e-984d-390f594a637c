@import url("https://fonts.googleapis.com/css2?family=Lato:wght@300;400;600&display=swap");

/* This is a CSS reset and global styling rule that applies to all elements (*) */
* {
  margin: 0;
  padding: 0;
  font-family: "Lato", sans-serif;
  box-sizing: border-box;
  color: white;
  font-weight: 600;
}

body {
  background: url(images/bg.jpg);
  width: 100%;
  height: 100dvh;
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

body::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100dvh;
  background: rgb(0, 0, 0, 0.15);
  backdrop-filter: blur(15px);
}
.card {
  width: 300px;
  height: 496px;
  border: solid;
  z-index: 1;
  background: linear-gradient(
    to top,
    rgb(0, 0, 0, 0.15),
    rgb(255, 255, 255, 0.15)
  );
  border-radius: 12px;
  backdrop-filter: blur(100px);
  padding: 20px;
}

.search {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 25px;
}

.search input {
  background: rgb(0, 0, 0, 0.15);
  padding: 10px 16px;
  border-radius: 99px;
  border: 3px solid transparent;
  flex: 1;
  font-weight: 500;
  margin-right: 16px;
  transition: 0.25s border;
  outline: none;
}

.search input:focus {
  border: 3px solid rgb(0, 0, 0, 0.15);
}

.search input::placeholder {
  color: rgb(255, 255, 255, 0.75);
}
.search button {
  outline: 0;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  cursor: pointer;
  background: rgb(0, 0, 0, 0.15);
  border: 3px solid transparent;
  transition: 0.25s border;
  outline: none;
}

.search button:focus {
  border: 3px solid rgb(0, 0, 0, 0.15);
}

.search button img {
  width: 16px;
}

.weather-info {
  display: flex;
  flex-direction: column;
  gap: 25px;
}

.regular-text {
  font-weight: 400;
}

.loaction-date-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.location {
  display: flex;
  align-items: center;
  gap: 6px;
}

.weather-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.weather-summary-img {
  width: 120px;
  height: 120px;
}

.weather-summary-info {
  text-align: end;
}

.weather-conditions {
  display: flex;
  justify-content: space-between;
}

.condition-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.condition-item span {
  font-size: 30px;
}

.forecast-items {
  display: flex;
  gap: 15px;
  overflow-x: scroll;
  padding-bottom: 12px;
}

.forecast-items::-webkit-scrollbar {
  height: 8px;
}

.forecast-items::-webkit-scrollbar-track {
  background: rgb(0, 0, 0, 0.1);
  border-radius: 99px;
}

.forecast-items::-webkit-scrollbar-thumb {
  background: rgb(0, 0, 0, 0.15);
  border-radius: 99px;
}

.forecast-item {
  min-width: 70px;
  background: rgb(255, 255, 255, 0.1);
  display: flex;
  flex-direction: column;
  gap: 6px;
  padding: 10px;
  align-items: center;
  border-radius: 12px;
  transition: 0.3s background;
}

.forecast-item:hover {
  background: rgb(255, 255, 255, 0.15);
}

.forecast-item-img {
  width: 35px;
  height: 35px;
}

.section-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  gap: 15px;
  margin-top: 25%;
}

.section-message img {
  height: 180px;
  width: fit-content;
}
