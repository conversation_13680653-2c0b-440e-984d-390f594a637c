* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  background: linear-gradient(to right, #0f2027, #203a43, #2c5364);
  font-family: "Segoe UI", sans-serif;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.container {
  width: 100%;
  max-width: 1200px;
  padding: 20px;
}

.carousel-wrapper {
  position: relative;
  width: 100%;
}

.carousel {
  display: flex;
}

.card {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  text-align: center;
  padding: 20px;
  backdrop-filter: blur(10px);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.card img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin-bottom: 15px;
  border: 3px solid white;
}

.card h3 {
  margin-bottom: 5px;
}

.card p {
  margin-bottom: 10px;
}

.card button {
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  background: white;
  color: #333;
  cursor: pointer;
  font-weight: bold;
}

.arrow {
  color: white;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  top: 50%;
  transform: translateY(-50%);
}

.swiper-button-prev {
  left: 10px;
}

.swiper-button-next {
  right: 10px;
}

.swiper-pagination {
  text-align: center;
  margin-top: 10px;
  position: static;
}

.swiper-pagination-bullet {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  margin: 0 5px;
  cursor: pointer;
}

.swiper-pagination-bullet-active {
  background: white;
}

/* Responsive Card Widths */
.swiper-slide {
  width: auto;
}

@media (max-width: 1024px) {
  .card {
    max-width: 320px;
  }
}

@media (max-width: 768px) {
  .card {
    max-width: 90%;
  }
}
